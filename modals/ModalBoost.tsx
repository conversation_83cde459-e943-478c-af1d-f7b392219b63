"use client";

import React, { useState, useEffect, useMemo } from "react";
import { CloseIcon, FlashOn, QuestionIcon, SuiIconBoost } from "@/assets/icons";
import { BgCardBoost } from "@/public/images";
import { AppButton } from "@/components";
import rf from "@/services/RequestFactory";
import { toastError, toastSuccess } from "@/libs/toast";
import { TPair } from "@/types";
import Image from "next/image";
import { NETWORKS } from "@/utils/contants";
import { PaymentWalletSelector } from "@/components/PaymentWalletSelector";
import { RootState } from "@/store";
import { useSelector } from "react-redux";
import { ICheckboxIcon, ICheckedIcon } from "@/public/images";
import { useNetwork } from "@/context";

const boostData = [
  { label: "5H", sui: 10, popular: false },
  { label: "12H", sui: 30, popular: false },
  { label: "24H", sui: 50, save: 10, popular: true },
  { label: "2D", sui: 80, save: 40, popular: true },
  // { label: "7D", sui: 250, save: 170, popular: false },
];

const ModalBoost = ({
  isOpen,
  onClose,
  pair,
}: {
  isOpen: boolean;
  onClose: () => void;
  pair: TPair;
}) => {
  const wallets = useSelector((state: RootState) => state.user.wallets);
  const [selectedPack, setSelectedPack] = useState<number | null>(null);
  const [agreed, setAgreed] = useState(false);
  const [selectedWalletAddress, setSelectedWalletAddress] =
    useState<string>("");
  const { currentNetwork } = useNetwork();

  const boostFactorMap: { [key: number]: number } = {
    0: 60 * 60 * 5,
    1: 60 * 60 * 12,
    2: 60 * 60 * 24,
    3: 60 * 60 * 24 * 2,
    4: 60 * 60 * 24 * 7,
  };

  const selectedBoostFactor = boostFactorMap[selectedPack!];

  const onTransferBoost = async () => {
    try {
      await rf.getRequest("WithdrawRequest").transfer(currentNetwork, {
        amountIn: "",
        fromWallet: selectedWalletAddress,
        metadata: {
          tokenAddress: pair?.tokenBase?.address,
          boostFactor: selectedBoostFactor,
        },
        paymentCategory: "BOOST_TOKEN_TRENDING",
        toWallet: "",
        withdrawalType: "payment",
      });
      toastSuccess("Success", "The transfer has been completed successfully!");
      onClose();
    } catch (e: any) {
      toastError("Error", e?.message || "Something went wrong!");
      console.error(e);
    }
  };

  const selectedWallet = useMemo(
    () => wallets.find((item) => item.address === selectedWalletAddress),
    [wallets, selectedWalletAddress]
  );

  const isSufficientBalance = useMemo(() => {
    if (selectedPack === null || !selectedWallet) return false;
    const requiredAmount = boostData[selectedPack].sui;
    return Number(selectedWallet.balance) < requiredAmount;
  }, [selectedPack, selectedWallet]);

  useEffect(() => {
    return () => {
      setSelectedPack(null);
      setSelectedWalletAddress("");
    };
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div
      style={{ backdropFilter: "blur(16px)" }}
      className="bg-black-500 fixed inset-0 z-50 flex items-center justify-center bg-opacity-80"
    >
      <div
        style={{
          background: `linear-gradient(0deg, #08090C, #08090C), linear-gradient(0deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.1))`,
          backgroundBlendMode: "overlay",
        }}
        className="border-white-100 relative w-[343px] max-w-[900px] overflow-hidden rounded-[12px] border md:w-full"
      >
        <div className="text-white-1000 absolute right-4 top-4 cursor-pointer">
          <CloseIcon onClick={onClose} />
        </div>

        <div className="p-4 text-center">
          <h2 className="heading-md-semibold-18 text-white-1000 flex flex-wrap items-center justify-center gap-1">
            Give{" "}
            <span className="text-brand-500 ml-1">{pair?.tokenBase?.name}</span>
            <span className="ml-1 inline-flex items-center">
              a <FlashOn className="text-brand-500 mx-1" />
              <span className="text-brand-500">Boost</span>
            </span>
          </h2>

          <div className="body-md-regular-14 text-white-1000 mt-2 flex flex-wrap items-center justify-center gap-1">
            Showcase your support, boosts Trending Score and unlock Golden
            ticker!
          </div>

          <div className="mt-3 flex items-center justify-center gap-1">
            <QuestionIcon />
            <a href="https://raidenx.io/docs/trending-boost" target="_blank">
              <div className="text-brand-500 action-sm-medium-12 cursor-pointer">
                How does it work?
              </div>
            </a>
          </div>
        </div>

        <div className="flex items-center justify-center px-4">
          <div className="border-white-100 flex-grow border-b"></div>
          <div className="text-white-1000 body-md-medium-14 mx-4 whitespace-nowrap">
            Choose a boost pack
          </div>
          <div className="border-white-100 flex-grow border-b"></div>
        </div>

        <div className="flex flex-col items-center justify-center gap-6 p-4">
          {selectedPack === null ? (
            <div className="grid w-full grid-cols-1 gap-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 lg:gap-5">
              {boostData.map(({ label, sui, save, popular }, index) => (
                <div
                  key={index}
                  onClick={() => setSelectedPack(index)}
                  className="relative w-full cursor-pointer overflow-hidden text-center transition-transform hover:scale-[1.02]"
                >
                  {popular && (
                    <div className="text-black-900 body-sm-semibold-12 absolute left-[-68px] top-2 z-10 w-[217px] -rotate-45 transform bg-[#FCD34B] px-2 py-1">
                      Popular
                    </div>
                  )}

                  <div className="relative h-full w-full">
                    <Image
                      src={BgCardBoost}
                      alt="bg-card-boost"
                      className="h-auto w-full rounded-lg"
                      width={343}
                      height={100}
                    />
                    <div className="absolute inset-0 flex flex-col items-center justify-center gap-2 px-2">
                      <FlashOn className="text-brand-500 mb-2 h-[40px] w-[40px]" />
                      <div className="text-brand-500 heading-2xl-semibold-40">
                        {label}
                      </div>
                      <div className="heading-lg-semibold-24 text-white-1000 flex items-center gap-2">
                        <span>{sui}</span>
                        <SuiIconBoost />
                      </div>

                      {save ? (
                        <div className="body-sm-regular-12 text-orange-500">
                          SAVE {save} SUI
                        </div>
                      ) : (
                        <div className="h-[20px]"></div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="w-full">
              <div className="flex flex-col items-center gap-6">
                <div className="relative text-center">
                  <Image
                    src={BgCardBoost}
                    alt="bg-card-boost"
                    className="h-auto w-full rounded-lg"
                    width={343}
                    height={100}
                  />
                  <div className="absolute inset-0 flex flex-col items-center justify-center gap-2 px-2">
                    <FlashOn className="text-brand-500 mb-2 h-[40px] w-[40px]" />
                    <div className="text-brand-500 heading-2xl-semibold-40">
                      {boostData[selectedPack].label}
                    </div>
                    <div className="heading-lg-semibold-24 text-white-1000 flex items-center gap-2">
                      <span>{boostData[selectedPack].sui}</span>
                      <SuiIconBoost />
                    </div>
                    {boostData[selectedPack].save && (
                      <div className="body-sm-regular-12 text-orange-500">
                        SAVE {boostData[selectedPack].save} SUI
                      </div>
                    )}
                  </div>
                </div>
              </div>
              <div>
                <div className="action-sm-medium-12 text-white-700 mb-2">
                  Choose wallet
                </div>

                <div>
                  <PaymentWalletSelector
                    setSelectedWalletAddress={setSelectedWalletAddress}
                    isOnlySelect
                  />
                </div>
              </div>
              <div className="mt-8 flex items-center justify-center">
                <div
                  className="flex cursor-pointer items-center gap-2"
                  onClick={() => setAgreed(!agreed)}
                >
                  {agreed ? (
                    <img src={ICheckedIcon.src} alt="Checked" />
                  ) : (
                    <img src={ICheckboxIcon.src} alt="Checkbox" />
                  )}
                  <div className="body-md-regular-14 text-white-1000 cursor-pointer select-none whitespace-nowrap">
                    I agree to the boosting Terms and Conditions
                  </div>
                </div>
              </div>
              <AppButton
                size="large"
                variant="buy"
                className="mt-4 w-full"
                onClick={onTransferBoost}
                disabled={!agreed || isSufficientBalance}
              >
                Pay {boostData[selectedPack].sui} SUI
              </AppButton>
              {isSufficientBalance && (
                <div className="mt-2 text-[12px] text-red-500">
                  Insufficient balance. Please select a different wallet.
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ModalBoost;
