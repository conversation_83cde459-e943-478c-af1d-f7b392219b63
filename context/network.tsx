"use client";

import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  ReactNode,
} from "react";
import { NETWORKS } from "@/utils/contants";
import Storage from "@/libs/storage";
import {
  getNetworkConfig,
  NETWORK_CONFIGS,
  type NetworkType,
} from "@/app/providers/networkChains";

interface NetworkContextType {
  currentNetwork: NETWORKS;
  setCurrentNetwork: (network: NETWORKS) => void;
  isNetworkLoaded: boolean;
  networkConfig: (typeof NETWORK_CONFIGS)[NetworkType] | null;
}

const NetworkContext = createContext<NetworkContextType>({
  currentNetwork: Storage.getNetwork() || NETWORKS.SUI,
  setCurrentNetwork: () => {},
  isNetworkLoaded: false,
  networkConfig: null,
});

export const useNetwork = () => {
  const context = useContext(NetworkContext);
  if (!context) {
    throw new Error("useNetwork must be used within a NetworkProvider");
  }
  return context;
};

interface NetworkProviderProps {
  children: ReactNode;
}

export const NetworkProvider: React.FC<NetworkProviderProps> = ({
  children,
}) => {
  const [currentNetwork, setCurrentNetworkState] = useState<NETWORKS>(() => {
    // Initialize from storage only on client side
    if (typeof window !== "undefined") {
      return Storage.getNetwork() || NETWORKS.SUI;
    }
    return NETWORKS.SUI;
  });
  const [isNetworkLoaded, setIsNetworkLoaded] = useState(false);

  // Initialize network from localStorage
  useEffect(() => {
    const savedNetwork = Storage.getNetwork() || NETWORKS.SUI;
    if (savedNetwork !== currentNetwork) {
      setCurrentNetworkState(savedNetwork);
    }
    setIsNetworkLoaded(true);
  }, []);

  const setCurrentNetwork = (network: NETWORKS) => {
    setCurrentNetworkState(network);
    Storage.setNetwork(network);

    Storage.clearPairSearch();
    Storage.clearHistorySearch();

    if (typeof window !== "undefined") {
      window.location.reload();
    }
  };

  const value: NetworkContextType = {
    currentNetwork,
    setCurrentNetwork,
    isNetworkLoaded,
    networkConfig: getNetworkConfig(currentNetwork),
  };

  return (
    <NetworkContext.Provider value={value}>{children}</NetworkContext.Provider>
  );
};
