"use client";

import { default as React, useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { Setting } from "@/assets/icons";
import { ModalFilterPair } from "@/modals";
import { RootState } from "@/store";
import { filterParams } from "@/utils/helper";

interface IDataParams {
  dexes: string[];
  advancedFilters: any[];
  auditCheck: string[];
}

export const ButtonFilterPair = ({
  params,
  setParams,
  height = 42,
  showText = true,
}: {
  params: any;
  height?: number;
  setParams: (params: any) => void;
  showText?: boolean;
}) => {
  const [isShowFilter, setIsShowFilter] = useState<boolean>(false);
  const [dataParams, setDataParams] = useState<IDataParams>({
    dexes: [],
    advancedFilters: [],
    auditCheck: [],
  });

  const { dexes: listDex } = useSelector((state: RootState) => state.metadata);

  useEffect(() => {
    if (!!dataParams.dexes.length) return;
    const listName = listDex.map((item: any) => item.dex);
    setDataParams({
      ...dataParams,
      dexes: listName,
    });
  }, [listDex]);

  useEffect(() => {
    const advancedFilters: Array<any> = [];
    const auditCheck: Array<any> = [];

    // Convert params to dataParams format
    if (params.dexes) {
      setDataParams((prev) => ({
        ...prev,
        dexes: params.dexes.split(","),
      }));
    }

    // Handle audit checks
    if (params.mintAuthDisabled) auditCheck.push("mintAuthDisabled");
    if (params.freezeAuthDisabled) auditCheck.push("freezeAuthDisabled");
    if (params.lpBurned) auditCheck.push("lpBurned");
    if (params.top10Holders) auditCheck.push("top10Holders");
    if (params.devBalance) auditCheck.push("devBalance");

    // Handle advanced filters
    const filterFields = [
      { name: "Liquidity", value: "liquidity" },
      { name: "Volume", value: "volume" },
      { name: "Market Cap", value: "marketCap" },
      { name: "Txns", value: "txns" },
    ];

    filterFields.forEach((field) => {
      if (params[field.value]) {
        const [from, to] = params[field.value].split("-");
        advancedFilters.push({
          name: field.name,
          value: field.value,
          from,
          to,
        });
      }
    });

    setDataParams((prev) => ({
      ...prev,
      advancedFilters,
      auditCheck,
    }));
  }, [params]);

  return (
    <div className="tablet:w-[70px] flex items-center">
      <div
        style={{ height: `${height}px` }}
        onClick={() => setIsShowFilter(true)}
        className="tablet:h-[42px] bg-white-100 hover:bg-white-50 body-sm-medium-12 flex aspect-square h-[32px] cursor-pointer items-center justify-center gap-[4px] rounded-[6px] px-[8px] md:h-[36px]"
      >
        <div className="relative">
          <Setting />
          {!!Object.keys(filterParams(params)).length && (
            <div className="bg-brand-500 absolute right-[-2px] top-[-2px] h-[6px] w-[6px] rounded-full" />
          )}
        </div>
        {showText && <span>Filters</span>}
      </div>

      <ModalFilterPair
        dataParams={dataParams}
        setDataParams={setDataParams}
        listDex={listDex}
        setParams={setParams}
        onClose={() => setIsShowFilter(false)}
        isOpen={isShowFilter}
      />
    </div>
  );
};
