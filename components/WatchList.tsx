"use client";
import { debounce } from "lodash";
import React, { memo, useContext, useEffect, useRef, useState } from "react";
import { SearchIcon } from "@/assets/icons";
import {
  AppButtonSort,
  AppCopy,
  AppNumber,
  BaseToken,
  PairFavourite,
  PairSocials,
} from "@/components";
import { TableFavouritePair } from "@/components/Table/TableFavouritePair";
import {
  AppBroadcast,
  BROADCAST_EVENTS,
  TBroadcastEvent,
} from "@/libs/broadcast";
import { TPair } from "@/types";
import { formatNumber } from "@/utils/format";
import Link from "next/link";
import { TRADE_TYPE } from "@/enums/trade.enum";
import { getCirculatingSupply } from "@/utils/pair";
import { isDexHasBondingCurve } from "../utils/dex";
import { RootPairContext } from "@/app/[network]/(token_detail)/provider";

const FavouriteItemInPairDetail = memo(({ item }: { item: TPair }) => {
  const CellItemWrapper = React.memo(
    ({
      className = "",
      children,
    }: {
      className?: string;
      children: React.ReactNode;
    }) => (
      <td className={className}>
        <Link href={`/${item.network}/${encodeURI(item.slug)}`}>
          {children}
        </Link>
      </td>
    )
  );
  CellItemWrapper.displayName = "CellItemWrapper";

  const circulatingSupply = getCirculatingSupply(item as unknown as TPair);

  return (
    <>
      <CellItemWrapper className="w-[14px]">
        <div className="flex justify-center px-[4px]">
          <PairFavourite pair={item} />
        </div>
      </CellItemWrapper>

      <CellItemWrapper className="w-[84px]">
        <div className="border-white-50 flex h-[62px] items-center gap-[3px] border-r md:border-0">
          <BaseToken pair={item} />
          <div>
            <div className="flex items-center gap-1">
              <div className="body-sm-regular-11 max-w-[35px] truncate">
                {item?.tokenBase?.symbol || "Unknown"}
              </div>
              <div onClick={(e) => e.preventDefault()}>
                <AppCopy
                  message={item.tokenBase.address}
                  className="text-white-700 hover:text-white-1000"
                />
              </div>
            </div>

            <div className="flex items-center gap-1">
              <PairSocials info={item?.tokenBase?.socials} />
            </div>
          </div>
        </div>
      </CellItemWrapper>

      <CellItemWrapper className="w-[44px]">
        <div className="td body-sm-regular-11 h-full w-full flex-col items-end justify-center ">
          <div>{formatNumber(item?.stats?.totalNumTxn["24h"], 2)}</div>
        </div>
      </CellItemWrapper>

      <CellItemWrapper className="w-[60px]">
        <div className="td body-sm-regular-11 text-white-600 h-full w-full flex-col items-end justify-center">
          <AppNumber
            value={Number(item?.priceUsd ?? 0) * Number(circulatingSupply ?? 0)}
            isForUSD
          />
        </div>
      </CellItemWrapper>

      <CellItemWrapper className="w-[60px]">
        <div className="td body-sm-regular-11 h-full w-full flex-col items-end justify-center">
          <div
            className={
              +item?.stats?.percent["24h"] > 0
                ? "text-green-500"
                : "text-red-500"
            }
          >
            {item?.stats?.percent["24h"]
              ? `${formatNumber(item?.stats?.percent["24h"], 2)}%`
              : "--"}
          </div>
        </div>
      </CellItemWrapper>
    </>
  );
});
FavouriteItemInPairDetail.displayName = "FavouriteItemInPairDetail";

export const WatchList = () => {
  const ref = useRef<HTMLDivElement>(null);
  const favouriteTableRef = useRef<HTMLDivElement>(null);
  // const [tableHeight, setTableHeight] = useState(0);

  const [sortBy, setSortBy] = useState<string>("");
  const [sortType, setSortType] = useState<string>("");
  const [search, setSearch] = useState<string | undefined>(undefined);
  // const [otherElementsHight, setOtherElementsHight] = useState(45);

  const { pair } = useContext(RootPairContext) as {
    pair: TPair;
    reserveQuote: string;
    reserveBase: string;
  };

  // useEffect(() => {
  //   if (ref?.current) {
  //     setTableHeight(ref?.current.offsetHeight);
  //   }
  // }, []);
  //
  // const handleOtherElementHeight = (event: TBroadcastEvent) => {
  //   const type = event?.detail;
  //   if (ref.current) setOtherElementsHight(type === TRADE_TYPE.BUY ? 45 : 0);
  // };
  //
  // useEffect(() => {
  //   AppBroadcast.on(
  //     BROADCAST_EVENTS.CHANGE_TYPE_ORDER_FORM,
  //     handleOtherElementHeight
  //   );
  //
  //   return () =>
  //     AppBroadcast.remove(
  //       BROADCAST_EVENTS.CHANGE_TYPE_ORDER_FORM,
  //       handleOtherElementHeight
  //     );
  // }, []);

  const renderWatchListTableHeader = () => {
    return (
      <tr className="body-sm-regular-11 text-white-500 h-[31px] bg-[#1f2026]">
        <th className="w-[14px] px-[4px] py-[6px]" />

        <th className="w-[84px] px-[4px] py-[6px]">
          <div className="border-white-50 border-r text-left md:border-0">
            Token
          </div>
        </th>

        <th className="w-[44px] px-[4px] py-[6px] text-end">
          <div className="flex items-center justify-end gap-1">
            Tnxs
            <AppButtonSort
              value="tnxs"
              sortBy={sortBy}
              sortType={sortType}
              setSortType={setSortType}
              setSortBy={setSortBy}
            />
          </div>
        </th>

        <th className="w-[60px] px-[4px] py-[6px] text-end">
          <div className="flex items-center justify-end gap-1">
            Mcap
            <AppButtonSort
              value="mc"
              sortBy={sortBy}
              sortType={sortType}
              setSortType={setSortType}
              setSortBy={setSortBy}
            />
          </div>
        </th>

        <th className="w-[60px] px-[4px] py-[6px] text-end">
          <div className="flex items-center justify-end gap-1">
            24h
            <AppButtonSort
              value="percent_24h"
              sortBy={sortBy}
              sortType={sortType}
              setSortType={setSortType}
              setSortBy={setSortBy}
            />
          </div>
        </th>
      </tr>
    );
  };

  const renderWatchListTableRow = (item: TPair) => {
    return <FavouriteItemInPairDetail item={item} />;
  };

  const onFilter = debounce(
    (sortByValue: string, sortTypeValue: string, searchValue: string) => {
      (favouriteTableRef.current as any)?.filter({
        sortBy: sortByValue,
        sortType: sortTypeValue,
        search: searchValue,
      });
    },
    300
  );

  useEffect(() => {
    if ((!sortBy || !sortType) && search === undefined) {
      return;
    }
    onFilter(sortBy, sortType, search || "");
  }, [sortBy, sortType, search, onFilter]);

  const onInputSearchChange = (event: any) => {
    const value = event.target.value;
    setSearch(value);
  };

  return (
    <div ref={ref} className="h-full">
      <div className="bg-black-900 border-neutral-alpha-100 my-[7px] flex w-full items-center gap-1 rounded-[6px] border p-[8px]">
        <SearchIcon className="h-[13px] w-[13px]" />
        <input
          className="body-sm-regular-11 flex-1 truncate bg-transparent leading-none outline-none"
          value={search}
          onChange={onInputSearchChange}
          placeholder="Search"
        />
      </div>
      <TableFavouritePair
        ref={favouriteTableRef}
        tableHeight={
          isDexHasBondingCurve(pair?.dex?.dex)
            ? `calc(100vh - 530px)`
            : "calc(100vh - 480px)"
        }
        customRenderHeader={renderWatchListTableHeader}
        customRenderRow={renderWatchListTableRow}
      />
    </div>
  );
};
