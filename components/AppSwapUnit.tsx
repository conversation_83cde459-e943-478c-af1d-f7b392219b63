import { SwapMoneyIcon } from "@/assets/icons";
import * as React from "react";
import { TPair } from "@/types";

const UNIT_TYPE = {
  USD: "USD",
  TOKEN: "TOKEN",
};

export const AppSwapUnit = ({
  unit,
  setUnit,
  pair,
  label,
}: {
  label?: string;
  unit: string;
  setUnit: (value: string) => void;
  pair?: TPair;
}) => {
  const handleSwap = () => {
    if (unit === UNIT_TYPE.USD) {
      setUnit(UNIT_TYPE.TOKEN);
      return;
    }

    setUnit(UNIT_TYPE.USD);
    return;
  };

  return (
    <div
      className="text-neutral-alpha-1000 bg-neutral-alpha-50 flex cursor-pointer items-center gap-1 rounded-[4px] px-1 py-[2px] text-[10px] leading-[16px]"
      onClick={handleSwap}
    >
      {unit === "USD"
        ? "USD"
        : label || `${pair?.tokenQuote?.symbol?.toUpperCase() || "SUI"}`}
      <SwapMoneyIcon />
    </div>
  );
};
