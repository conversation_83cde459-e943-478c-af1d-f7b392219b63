"use client";
import clsx from "clsx";
import * as React from "react";
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useMediaQuery } from "react-responsive";
import { BuyIcon, ChevronDownIcon, TagIcon, WalletIcon } from "@/assets/icons";
import { AppLogoNetwork } from "@/components";
import { AppBroadcast, BROADCAST_EVENTS } from "@/libs/broadcast";
import { ModalBottomMobile } from "@/modals";
import { WalletSelection } from "./WalletSelection";
import { RootState, AppDispatch } from "@/store";
import { setIsShowModalAddWallet } from "@/store/metadata.store";
import { TPair } from "@/types";
import { formatNumber } from "@/utils/format";
import DrawerIssuesDetected from "@/components/Pair/IssuesDetected";
import { SettingsForm } from "./SettingsForm";
import { useTradingWallet } from "@/hooks/useTradingWallet";
import { isPairWithNativeToken } from "@/utils/pair";
import { OrderFormType } from "@/enums";
import { TRADE_TYPE } from "@/enums/trade.enum";
import { NETWORKS } from "@/utils/contants";
import { OrderFormBuyMarket } from "./buy-order/market-order";
import { OrderFormBuyLimit } from "./buy-order/limit-order";
import { OrderFormBuyDCA } from "./buy-order/dca-order";
import { OrderFormSellMarket } from "./sell-order/market-order";
import { OrderFormSellLimit } from "./sell-order/limit-order";
import { OrderFormSellDCA } from "./sell-order/dca-order";
import { useMemo } from "react";
import {
  SUI_DECIMALS,
  SUI_TOKEN_ADDRESS_FULL,
  USDC_ADDRESS,
  USDC_DECIMALS,
} from "../../utils/contants";
import { AppAvatarTokenQuote } from "../AppAvatarToken";
import { EDex, isMemeDexForNetwork } from "../../enums/dex.enum";
import Image from "next/image";
import { isPairWithUSDC } from "../../utils/pair";
import { useNetwork } from "@/context";

interface OrderFormProps {
  pair: TPair;
}

export const OrderForm = ({ pair }: OrderFormProps) => {
  const [type, setType] = useState<number>(TRADE_TYPE.BUY);
  const [orderType, setOrderType] = useState<string>(OrderFormType.MARKET);
  const [isShowSettings, setIsShowSettings] = useState<boolean>(false);
  const [isShowSelectWallet, setIsShowSelectWallet] = useState<boolean>(false);
  const [isShowModalWalletSelection, setShowModalWalletSelection] =
    useState<boolean>(false);
  const [isShowModalSettingsOrder, setShowModalSettingsOrder] =
    useState<boolean>(false);
  const isTabletOrMobile = useMediaQuery({ query: "(max-width: 992px)" });
  const { currentNetwork } = useNetwork();

  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const wallets = useSelector((state: RootState) => state.user.wallets);
  const [addressTokenQuoteSelected, setAddressTokenQuoteSelected] =
    useState<string>(SUI_TOKEN_ADDRESS_FULL);
  const [isClient, setIsClient] = useState<boolean>(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  useEffect(() => {
    setAddressTokenQuoteSelected(pair?.tokenQuote?.address);
  }, [pair?.tokenQuote?.symbol]);

  useEffect(() => {
    if (type === TRADE_TYPE.SELL) {
      setAddressTokenQuoteSelected(pair?.tokenQuote?.address);
    }
    setOrderType(OrderFormType.MARKET);
  }, [type]);

  const {
    tradingWallets,
    activeTradingWallets,
    activeTotalQuoteBalance,
    activeTotalBaseBalance,
  } = useTradingWallet(pair?.tokenBase?.address, addressTokenQuoteSelected);

  const dispatch = useDispatch<AppDispatch>();

  const onAddWallet = () => {
    dispatch(setIsShowModalAddWallet({ isShow: true }));
  };

  const selectType = (type: number) => {
    AppBroadcast.dispatch(BROADCAST_EVENTS.CHANGE_TYPE_ORDER_FORM, type);
    setType(type);
  };

  const onShowSetting = () => {
    if (!accessToken) return;
    if (isTabletOrMobile) {
      setShowModalSettingsOrder(true);
      return;
    }
    setIsShowSettings(true);
  };

  const onShowSelectWallet = () => {
    if (!accessToken) return;
    if (isTabletOrMobile) {
      setShowModalWalletSelection(true);
      return;
    }
    setIsShowSelectWallet(true);
  };

  const isMemeDex = useMemo(
    () =>
      pair?.dex && isMemeDexForNetwork(pair?.dex.dex as EDex, currentNetwork),
    [pair, currentNetwork]
  );

  const listCurrency = useMemo(() => {
    let currencies = [
      {
        address: SUI_TOKEN_ADDRESS_FULL,
        symbol: "SUI",
        decimals: SUI_DECIMALS,
        icon: (
          <AppLogoNetwork
            network={currentNetwork}
            className={`h-[14px] w-[14px]`}
            isBase
          />
        ),
      },
    ];

    if (!isPairWithNativeToken(pair, currentNetwork)) {
      currencies = [
        ...currencies,
        {
          address: pair?.tokenQuote?.address,
          symbol: pair?.tokenQuote?.symbol,
          decimals: pair?.tokenQuote?.decimals,
          icon: <AppAvatarTokenQuote pair={pair} />,
        },
      ];
    }

    if (!isMemeDex && !isPairWithUSDC(pair)) {
      currencies = [
        ...currencies,
        {
          address: USDC_ADDRESS,
          symbol: "USDC",
          decimals: USDC_DECIMALS,
          icon: (
            <Image
              src={"/images/USDCIcon.png"}
              alt="usdc"
              width={14}
              height={14}
              className="rounded-full object-cover"
              unoptimized
            />
          ),
        },
      ];
    }
    return currencies;
  }, [pair, isMemeDex]);

  const tokenQuoteSelected = useMemo(
    () =>
      listCurrency.find((c: any) => c?.address === addressTokenQuoteSelected),
    [listCurrency, addressTokenQuoteSelected]
  );

  const symbolTokenQuoteSelected = useMemo(() => {
    if (!tokenQuoteSelected) return "";
    return tokenQuoteSelected?.symbol || "";
  }, [tokenQuoteSelected]);

  const renderOrderFormContent = () => {
    if (type === TRADE_TYPE.BUY) {
      return (
        <>
          <div
            className={orderType === OrderFormType.MARKET ? "block" : "hidden"}
          >
            <OrderFormBuyMarket
              onShowSettings={onShowSetting}
              onShowSelectWallet={onShowSelectWallet}
              totalBalanceTokenBase={activeTotalBaseBalance}
              addressTokenQuoteSelected={addressTokenQuoteSelected}
              setAddressTokenQuoteSelected={setAddressTokenQuoteSelected}
              setOrderType={setOrderType}
              orderType={orderType}
              listCurrency={listCurrency}
              symbolTokenQuoteSelected={symbolTokenQuoteSelected}
              activeTotalQuoteBalance={activeTotalQuoteBalance}
            />
          </div>
          <div
            className={orderType === OrderFormType.LIMIT ? "block" : "hidden"}
          >
            <OrderFormBuyLimit
              onShowSettings={onShowSetting}
              onShowSelectWallet={onShowSelectWallet}
              totalBalanceTokenBase={activeTotalBaseBalance}
              addressTokenQuoteSelected={addressTokenQuoteSelected}
              setAddressTokenQuoteSelected={setAddressTokenQuoteSelected}
              setOrderType={setOrderType}
              orderType={orderType}
              listCurrency={listCurrency}
              symbolTokenQuoteSelected={symbolTokenQuoteSelected}
              activeTotalQuoteBalance={activeTotalQuoteBalance}
            />
          </div>
          <div className={orderType === OrderFormType.DCA ? "block" : "hidden"}>
            <OrderFormBuyDCA
              onShowSettings={onShowSetting}
              onShowSelectWallet={onShowSelectWallet}
              totalBalanceTokenBase={activeTotalBaseBalance}
              addressTokenQuoteSelected={addressTokenQuoteSelected}
              setAddressTokenQuoteSelected={setAddressTokenQuoteSelected}
              setOrderType={setOrderType}
              orderType={orderType}
              listCurrency={listCurrency}
              symbolTokenQuoteSelected={symbolTokenQuoteSelected}
              activeTotalQuoteBalance={activeTotalQuoteBalance}
            />
          </div>
        </>
      );
    }
    return (
      <>
        <div
          className={orderType === OrderFormType.MARKET ? "block" : "hidden"}
        >
          <OrderFormSellMarket
            pair={pair}
            onShowSettings={onShowSetting}
            onShowSelectWallet={onShowSelectWallet}
            totalBalanceTokenBase={activeTotalBaseBalance}
            setOrderType={setOrderType}
            orderType={orderType}
          />
        </div>
        <div className={orderType === OrderFormType.LIMIT ? "block" : "hidden"}>
          <OrderFormSellLimit
            pair={pair}
            onShowSettings={onShowSetting}
            onShowSelectWallet={onShowSelectWallet}
            totalBalanceTokenBase={activeTotalBaseBalance}
            setOrderType={setOrderType}
            orderType={orderType}
          />
        </div>
        <div className={orderType === OrderFormType.DCA ? "block" : "hidden"}>
          <OrderFormSellDCA
            pair={pair}
            onShowSettings={onShowSetting}
            onShowSelectWallet={onShowSelectWallet}
            totalBalanceTokenBase={activeTotalBaseBalance}
            setOrderType={setOrderType}
            orderType={orderType}
          />
        </div>
      </>
    );
  };

  const _renderBalance = () => {
    return (
      <div className="flex items-center gap-1">
        {tokenQuoteSelected?.icon}
        <div className="body-sm-regular-12">
          {formatNumber(activeTotalQuoteBalance)}
        </div>
      </div>
    );
  };

  const _renderContent = () => {
    if (isShowSettings) {
      return (
        <SettingsForm
          pair={pair}
          orderType={orderType}
          onCloseSettings={() => setIsShowSettings(false)}
        />
      );
    }

    if (isShowSelectWallet) {
      return (
        <WalletSelection
          pair={pair}
          tradingWallets={tradingWallets}
          type={type}
          onCloseWalletSettings={() => setIsShowSelectWallet(false)}
          tokenQuoteSelected={tokenQuoteSelected}
        />
      );
    }

    return (
      <>
        <div className="tablet:block hidden">
          {!!wallets.length ? (
            <div
              onClick={() => setIsShowSelectWallet(true)}
              className="bg-neutral-alpha-50 flex cursor-pointer items-center justify-between rounded-[4px] p-[4px]"
            >
              <div className="flex items-center gap-2">
                <div className="tablet:bg-neutral-beta-500 flex h-6 w-6 items-center justify-center rounded-[4px]">
                  <WalletIcon />
                </div>
                <div className="flex gap-2">
                  <div className="body-sm-semibold-12">Wallet</div>
                  <div className="body-xs-regular-10 text-brand-500 bg-brand-800 border-brand-800 rounded-[80px] border px-[6px]">
                    {activeTradingWallets.length} selected
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                {_renderBalance()}

                <div>
                  <ChevronDownIcon className="text-neutral-alpha-500 h-[16px] w-[16px] rotate-[-90deg]" />
                </div>
              </div>
            </div>
          ) : (
            <div className="bg-neutral-alpha-50 flex items-center justify-between rounded-[4px] p-[4px]">
              <div className="flex items-center gap-2">
                <div className="bg-neutral-beta-500 flex h-6 w-6 items-center justify-center rounded-[4px]">
                  <WalletIcon />
                </div>
                <div className="flex gap-2">
                  <div className="body-sm-semibold-12">Wallet</div>
                  {/*{accessToken && (*/}
                  {/*  <div className="body-sm-regular-12 text-neutral-alpha-500">*/}
                  {/*    {wallets.length} wallet*/}
                  {/*  </div>*/}
                  {/*)}*/}
                </div>
              </div>
              {accessToken && (
                <div
                  className="action-xs-medium-12 text-brand-500 cursor-pointer"
                  onClick={onAddWallet}
                >
                  Add Wallet
                </div>
              )}
            </div>
          )}
        </div>

        <div className="tablet:grid bg-neutral-beta-500 mb-[8px] mt-[12px] hidden grid-cols-2 rounded-[4px] p-[4px]">
          <div
            className={`${
              type === TRADE_TYPE.BUY
                ? "bg-white-100 text-white-1000"
                : "text-neutral-alpha-500"
            } body-sm-semibold-14 flex cursor-pointer items-center justify-center gap-1 rounded-[4px] px-[8px] py-[6px] text-center`}
            onClick={() => selectType(TRADE_TYPE.BUY)}
          >
            <BuyIcon /> Buy
          </div>
          <div
            className={`${
              type === TRADE_TYPE.SELL
                ? "bg-white-100 text-white-1000"
                : "text-neutral-alpha-500"
            } body-sm-semibold-14 flex cursor-pointer items-center justify-center gap-1 rounded-[4px] px-[8px] py-[6px] text-center`}
            onClick={() => selectType(TRADE_TYPE.SELL)}
          >
            <TagIcon /> Sell
          </div>
        </div>
        <div>{renderOrderFormContent()}</div>
      </>
    );
  };

  if (!isClient) {
    return null;
  }

  return (
    <div
      className={clsx(
        "tablet:h-auto bg-neutral-alpha-50 tablet:pr-[8px] border-neutral-alpha-50 tablet:overflow-hidden tablet:max-h-full flex h-full w-full overflow-auto border-b px-[8px] py-[12px]",
        isShowSelectWallet || isShowSettings ? "pr-2" : "pr-0"
      )}
    >
      <div className="w-full">{_renderContent()}</div>

      {!isShowSelectWallet && !isShowSettings && (
        <div
          className="tablet:hidden bg-neutral-alpha-100 ml-4 flex w-[36px] rotate-180 items-center justify-center gap-2 rounded-br-md rounded-tr-md text-center font-medium"
          style={{ writingMode: "vertical-rl" }}
          onClick={() =>
            selectType(
              type === TRADE_TYPE.BUY ? TRADE_TYPE.SELL : TRADE_TYPE.BUY
            )
          }
        >
          {type === TRADE_TYPE.BUY ? (
            <TagIcon className="rotate-90" />
          ) : (
            <BuyIcon className="rotate-90" />
          )}
          {type === TRADE_TYPE.BUY ? "Sell" : "Buy"}
        </div>
      )}

      {isShowModalWalletSelection && (
        <ModalBottomMobile
          isOpen={isShowModalWalletSelection}
          onClose={() => setShowModalWalletSelection(false)}
        >
          <div className="w-full p-[16px]">
            <WalletSelection
              pair={pair}
              tradingWallets={tradingWallets}
              type={type}
              onCloseWalletSettings={() => setShowModalWalletSelection(false)}
              tokenQuoteSelected={tokenQuoteSelected}
            />
          </div>
        </ModalBottomMobile>
      )}

      {isShowModalSettingsOrder && (
        <ModalBottomMobile
          isOpen={isShowModalSettingsOrder}
          onClose={() => setShowModalSettingsOrder(false)}
        >
          <div className="w-full p-[16px]">
            <SettingsForm
              pair={pair}
              onCloseSettings={() => setShowModalSettingsOrder(false)}
              orderType={orderType}
            />
          </div>
        </ModalBottomMobile>
      )}

      <DrawerIssuesDetected pair={pair} isLoading={false} />
    </div>
  );
};
