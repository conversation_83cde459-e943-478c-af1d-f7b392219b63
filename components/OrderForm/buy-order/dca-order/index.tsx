import BigNumber from "bignumber.js";
import * as React from "react";
import { useContext, useState } from "react";
import { AppDropdown } from "@/components";
import { useOrder, useAggregatorAutoToggle } from "@/hooks";
import { RootPairContext } from "@/app/[network]/(token_detail)/provider";
import { TPair, TPairPrice } from "@/types";
import { AmountForm } from "@/components/OrderForm/components/AmountForm";
import { OrderFormType } from "@/enums";
import { dividedBN, multipliedBN } from "@/utils/helper";
import { useEffect } from "react";
import { isEmpty } from "lodash";
import { getCirculatingSupply } from "@/utils/pair";
import { useMediaQuery } from "react-responsive";
import { DCAForm } from "@/components/OrderForm/components/DCAForm";
import { OPTIONS_ORDER_TYPE } from "@/utils/contants";
import { HeaderForm } from "@/components/OrderForm/components/HeaderForm";
import Storage from "@/libs/storage";
import { ButtonOrderSubmit } from "../ButtonOrderSubmit";
import { SwapSettingsBar } from "../SwapSettingsBar";
import { TRADE_TYPE } from "@/enums/trade.enum";

export const OrderFormBuyDCA = ({
  onShowSettings,
  onShowSelectWallet,
  totalBalanceTokenBase,
  addressTokenQuoteSelected,
  symbolTokenQuoteSelected,
  setAddressTokenQuoteSelected,
  activeTotalQuoteBalance,
  setOrderType,
  orderType,
  listCurrency,
}: {
  onShowSettings: () => void;
  onShowSelectWallet: () => void;
  totalBalanceTokenBase: string | number;
  addressTokenQuoteSelected: string;
  symbolTokenQuoteSelected: string;
  activeTotalQuoteBalance: string | number;
  setAddressTokenQuoteSelected: (value: string) => void;
  setOrderType: (value: string) => void;
  orderType: any;
  listCurrency: any[];
}) => {
  const [amount, setAmount] = useState<any>("");
  const orderSettings = Storage.getOrderSettings();
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // for DCA
  const [marketCapTo, setMarketCapTo] = useState<any>("");
  const [marketCapFrom, setMarketCapFrom] = useState<any>("");
  const [resolution, setResolution] = useState<string>("H");
  const [amountTime, setAmountTime] = useState<any>("");
  const [amountOrders, setAmountOrders] = useState<any>("");
  const [isSelectMCRange, setIsSelectMCRange] = useState<boolean>(false);

  const { pair, pairPrice } = useContext(RootPairContext) as {
    pair: TPair;
    pairPrice: TPairPrice;
    poolObjects: any;
  };

  const { useAggregator } = useAggregatorAutoToggle(pair?.createdAt);
  const { buyDCA } = useOrder();

  const isMobile = useMediaQuery({ query: "(max-width: 992px)" });

  const createOrder = async () => {
    if (new BigNumber(amount).isZero() || isLoading) return;

    let tokenPriceRange = {
      min: null,
      max: null,
    };

    if (isSelectMCRange) {
      let priceMin = null;
      let priceMax = null;

      if (!!marketCapFrom) {
        priceMin = dividedBN(marketCapFrom, getCirculatingSupply(pair));
      }

      if (!!marketCapTo) {
        priceMax = dividedBN(marketCapTo, getCirculatingSupply(pair));
      }

      tokenPriceRange = {
        min: priceMin,
        max: priceMax,
      } as any;
    }

    const getInterval = () => {
      if (!amountTime) return "";
      if (resolution === "D") return amountTime * 86400;
      if (resolution === "H") return amountTime * 3600;
      if (resolution === "M") return amountTime * 60;
      return "";
    };
    setIsLoading(true);
    await buyDCA(
      pair,
      amount,
      getInterval(),
      tokenPriceRange,
      amountOrders,
      addressTokenQuoteSelected,
      useAggregator,
      () => {
        setAmount("");
        setAmountOrders("");
        setAmountTime("");
        setIsSelectMCRange(false);
        setIsLoading(false);
      }
    ).then();
    setIsLoading(false);
  };

  useEffect(() => {
    if (isEmpty(pair) || orderType !== OrderFormType.DCA) return;
    const marketCap = multipliedBN(
      pairPrice?.priceUsd,
      getCirculatingSupply(pair)
    );

    setMarketCapTo(multipliedBN(marketCap, 1.1));
    setMarketCapFrom(multipliedBN(marketCap, 0.9));
  }, [pair?.pairId, orderType]);

  useEffect(() => {
    setAmount("");
  }, [addressTokenQuoteSelected]);

  const _renderTextBtn = () => {
    return (
      <>
        <div className="text-brand-500 md:body-md-medium-14 body-sm-medium-12">
          Buy {!!+amount && `${amount} ${symbolTokenQuoteSelected}`}
        </div>
        {amountTime && amountOrders && (
          <div className="body-xs-medium-10 text-white-500">
            every {amountTime}
            {resolution.toLowerCase()} over {amountOrders} orders
          </div>
        )}
      </>
    );
  };

  return (
    <div>
      <HeaderForm
        pair={pair}
        orderType={orderType}
        onShowSelectWallet={onShowSelectWallet}
        setOrderType={setOrderType}
        totalBalanceTokenBase={totalBalanceTokenBase}
        symbolTokenQuoteSelected={symbolTokenQuoteSelected}
        activeTotalQuoteBalance={activeTotalQuoteBalance}
        type={TRADE_TYPE.BUY}
      />

      {isMobile && (
        <div className="tablet bg-black-900 border-white-50 mt-3 flex h-[34px] items-center rounded-[4px] border">
          <AppDropdown
            options={OPTIONS_ORDER_TYPE}
            onSelect={(value) => {
              setOrderType(value);
            }}
            className="border-white-50 w-[64px] rounded-none border-r border-solid"
            value={orderType}
          />

          <div className="body-sm-regular-12 text-white-300 w-full text-center">
            Market Price
          </div>
        </div>
      )}

      <AmountForm
        value={amount}
        onChange={setAmount}
        setCurrencySelected={setAddressTokenQuoteSelected}
        currencySelected={addressTokenQuoteSelected}
        configs={{
          steps: {
            type: "fixed",
            values: orderSettings?.defaultBuyAmount,
          },
          multiCurrencyEnabled: listCurrency.length > 1,
          currencies: listCurrency,
        }}
      />

      <DCAForm
        pair={pair}
        marketCapTo={marketCapTo}
        marketCapFrom={marketCapFrom}
        resolution={resolution}
        amountOrders={amountOrders}
        amountTime={amountTime}
        isSelectMCRange={isSelectMCRange}
        setMarketCapTo={setMarketCapTo}
        setMarketCapFrom={setMarketCapFrom}
        setResolution={setResolution}
        setAmountOrders={setAmountOrders}
        setAmountTime={setAmountTime}
        setIsSelectMCRange={setIsSelectMCRange}
      />

      <>
        <SwapSettingsBar
          orderType={orderType}
          amount={amount}
          onShowSettings={onShowSettings}
          activeTotalQuoteBalance={activeTotalQuoteBalance}
        />

        <ButtonOrderSubmit
          isLoading={isLoading}
          amount={amount}
          pair={pair}
          text={_renderTextBtn()}
          onShowSettings={onShowSettings}
          createOrder={createOrder}
          symbolTokenQuoteSelected={symbolTokenQuoteSelected}
          addressTokenQuoteSelected={addressTokenQuoteSelected}
          activeTotalQuoteBalance={activeTotalQuoteBalance}
        />
      </>
    </div>
  );
};
