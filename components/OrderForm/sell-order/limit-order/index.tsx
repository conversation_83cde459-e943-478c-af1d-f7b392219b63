import BigNumber from "bignumber.js";
import { debounce, isEmpty } from "lodash";
import * as React from "react";
import { useCallback, useEffect, useRef, useState } from "react";
import { useSelector } from "react-redux";
import { OrderFormType, OrderLimitTargetType } from "@/enums";
import { useOrder, useAggregatorAutoToggle } from "@/hooks";
import Storage from "@/libs/storage";
import { toastError } from "@/libs/toast";
import { RootState } from "@/store";
import { TPair } from "@/types";
import { abs, dividedBN, multipliedBN, toStringBN } from "@/utils/helper";
import { getCirculatingSupply } from "@/utils/pair";
import { AmountForm } from "@/components/OrderForm/components/AmountForm";
import { TargetForm } from "@/components/OrderForm/components/TargetForm";
import { useMediaQuery } from "react-responsive";
import { usePairPrice } from "@/hooks/usePairPrice";
import { ButtonOrderSubmit } from "../ButtonOrderSubmit";
import { HeaderForm } from "@/components/OrderForm/components/HeaderForm";
import { formatNumber } from "@/utils/format";
import { AppDropdown } from "@/components";
import { OPTIONS_ORDER_TYPE } from "@/utils/contants";
import { AppNumber } from "../../../AppNumber";

export const OrderFormSellLimit = ({
  onShowSettings,
  onShowSelectWallet,
  totalBalanceTokenBase,
  setOrderType,
  orderType,
  pair,
}: {
  onShowSettings: () => void;
  onShowSelectWallet: () => void;
  setOrderType: (value: string) => void;
  totalBalanceTokenBase: string | number;
  orderType: any;
  pair: TPair;
}) => {
  const { pairPrice } = usePairPrice(pair);
  const userSettings = Storage.getUserSettings();
  const [sellPercent, setSellPercent] = useState<any>("");

  // for Limit
  const [targetMC, setTargetMC] = useState<any>("");
  const [targetType, setTargetType] = useState<any>(
    userSettings.orderLimitTargetType || OrderLimitTargetType.MC
  );
  const [targetPrice, setTargetPrice] = useState<any>("");
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // estimate
  const tokenAmountEstimateRef = useRef<any>("");
  const [tokenAmountEstimate, setTokenAmountEstimate] = useState<any>("");
  const targetConvertPercent = useRef("1");

  const { useAggregator } = useAggregatorAutoToggle(pair?.createdAt);

  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const { sellLimit, estimateQuickSell } = useOrder();

  const orderSettings = Storage.getOrderSettings();

  const isMobile = useMediaQuery({ query: "(max-width: 992px)" });

  useEffect(() => {
    if (isEmpty(pair)) return;
    setTargetMC(multipliedBN(pairPrice?.priceUsd, getCirculatingSupply(pair)));
    setTargetPrice(toStringBN(pairPrice?.priceUsd));
    Storage.setUserSettings("orderLimitTargetType", targetType);
  }, [pair?.pairId, targetType]);

  useEffect(() => {
    setSellPercent("");
  }, [orderType]);

  const debouncedEstimateQuickSell = useCallback(
    debounce((pair, sellPercent) => {
      estimateQuickSell(pair, +sellPercent).then((totalAmountOut) => {
        if (totalAmountOut) {
          tokenAmountEstimateRef.current = multipliedBN(
            totalAmountOut,
            targetConvertPercent.current
          );
          setTokenAmountEstimate(tokenAmountEstimateRef.current);
        }
      });
    }, 1500),
    [sellPercent, pair?.pairId]
  );

  useEffect(() => {
    tokenAmountEstimateRef.current = "";
    setTokenAmountEstimate(tokenAmountEstimateRef.current);
    if (!accessToken || !sellPercent) return;
    debouncedEstimateQuickSell(pair, sellPercent);

    return () => {
      debouncedEstimateQuickSell.cancel();
    };
  }, [accessToken, sellPercent, targetPrice, targetMC]);

  useEffect(() => {
    targetConvertPercent.current = "1";
    if (!accessToken || orderType !== OrderFormType.LIMIT) return;
    let rate;
    if (targetType === OrderLimitTargetType.MC) {
      rate = dividedBN(
        targetMC,
        multipliedBN(pairPrice?.priceUsd, getCirculatingSupply(pair))
      );
    } else if (targetType === OrderLimitTargetType.PRICE) {
      rate = dividedBN(targetPrice, pairPrice?.priceUsd);
    } else {
      return;
    }

    targetConvertPercent.current = rate;
    if (tokenAmountEstimateRef.current) {
      tokenAmountEstimateRef.current = multipliedBN(
        tokenAmountEstimateRef.current,
        rate
      );
      setTokenAmountEstimate(tokenAmountEstimateRef.current);
    }
  }, [accessToken, targetMC, targetPrice, targetType, orderType]);

  const onOrderSuccess = () => {
    setSellPercent("");
    setTokenAmountEstimate("");
    tokenAmountEstimateRef.current = "";
  };

  const createOrderSellLimit = async () => {
    if (isLoading) return;
    if (targetType === OrderLimitTargetType.MC) {
      const targetMcPercent = new BigNumber(
        dividedBN(
          targetMC,
          multipliedBN(pairPrice?.priceUsd, getCirculatingSupply(pair))
        )
      )
        .minus(1)
        .multipliedBy(100)
        .toFixed(8, BigNumber.ROUND_DOWN);

      if (!+targetMcPercent || +abs(targetMcPercent) < 0.1) {
        toastError("Error", "MC target too small!");
        return;
      }

      const targetPriceUsd = dividedBN(targetMC, getCirculatingSupply(pair));
      setIsLoading(true);
      await sellLimit(
        pair,
        +sellPercent,
        targetPriceUsd,
        +targetMcPercent,
        null,
        useAggregator,
        () => {
          onOrderSuccess();
          setIsLoading(false);
        }
      );
      setIsLoading(false);
      return;
    }

    const targetPricePercent = new BigNumber(
      dividedBN(targetPrice, pairPrice?.priceUsd)
    )
      .minus(1)
      .multipliedBy(100)
      .toFixed(8, BigNumber.ROUND_DOWN);

    if (!+targetPricePercent || +abs(targetPricePercent) < 0.1) {
      toastError("Error", "Price target too small!");
      return;
    }
    setIsLoading(true);
    await sellLimit(
      pair,
      +sellPercent,
      toStringBN(targetPrice),
      null,
      +targetPricePercent,
      useAggregator,
      () => {
        setIsLoading(false);
        onOrderSuccess();
      }
    );
    setIsLoading(false);
  };

  const createOrder = async () => {
    if (!+sellPercent || new BigNumber(sellPercent).isZero()) {
      toastError("Error", "Please input amount!");
      return;
    }

    if (!+sellPercent) {
      toastError("Error", "Amount too small!");
      return;
    }

    createOrderSellLimit().then();
    return;
  };

  const _renderTextBtn = () => {
    const sellAmount = new BigNumber(dividedBN(sellPercent, 100)).multipliedBy(
      totalBalanceTokenBase
    );
    return (
      <>
        <div className="md:body-md-medium-14 body-sm-medium-12 text-brand-500 flex items-center gap-1">
          Sell
          {!!+sellAmount && (
            <AppNumber
              value={sellAmount}
              decimals={pair?.tokenBase?.decimals}
            />
          )}
          {pair?.tokenBase?.symbol}
        </div>
        {!!+tokenAmountEstimate && (
          <div className="body-xs-medium-10 text-white-500">
            (≈
            {formatNumber(tokenAmountEstimate, pair?.tokenQuote.decimals)}{" "}
            {pair?.tokenQuote?.symbol})
          </div>
        )}
      </>
    );
  };

  return (
    <div>
      <HeaderForm
        pair={pair}
        orderType={orderType}
        onShowSelectWallet={onShowSelectWallet}
        setOrderType={setOrderType}
        totalBalanceTokenBase={totalBalanceTokenBase}
      />

      {isMobile && (
        <div className="tablet bg-black-900 border-white-50 mt-3 flex h-[34px] items-center rounded-[4px] border">
          <AppDropdown
            options={OPTIONS_ORDER_TYPE}
            onSelect={(value) => {
              setOrderType(value);
            }}
            className="border-white-50 w-[64px] rounded-none border-r border-solid"
            value={orderType}
          />

          <TargetForm
            pair={pair}
            targetType={targetType}
            setTargetType={setTargetType}
            targetMC={targetMC}
            setTargetMC={setTargetMC}
            targetPrice={targetPrice}
            setTargetPrice={setTargetPrice}
          />
        </div>
      )}

      <AmountForm
        value={sellPercent}
        onChange={setSellPercent}
        configs={{
          steps: {
            type: "percent",
            values: orderSettings?.defaultSellPercent,
          },
        }}
      />

      {!isMobile && (
        <div className="border-neutral-alpha-50 mt-6 overflow-hidden rounded-[8px] border">
          <TargetForm
            pair={pair}
            targetType={targetType}
            setTargetType={setTargetType}
            targetMC={targetMC}
            setTargetMC={setTargetMC}
            targetPrice={targetPrice}
            setTargetPrice={setTargetPrice}
          />
        </div>
      )}

      <ButtonOrderSubmit
        isLoading={isLoading}
        onShowSettings={onShowSettings}
        pair={pair}
        sellPercent={sellPercent}
        sellType={orderType}
        createOrder={createOrder}
        text={_renderTextBtn()}
      />
    </div>
  );
};
