import BigNumber from "bignumber.js";
import { debounce } from "lodash";
import * as React from "react";
import { useCallback, useEffect, useRef, useState } from "react";
import { useSelector } from "react-redux";
import { useOrder, useAggregatorAutoToggle } from "@/hooks";
import Storage from "@/libs/storage";
import { toastError } from "@/libs/toast";
import { RootState } from "@/store";
import { TPair } from "@/types";
import { multipliedBN } from "@/utils/helper";
import { AmountForm } from "@/components/OrderForm/components/AmountForm";
import { ButtonOrderSubmit } from "../ButtonOrderSubmit";
import { dividedBN } from "@/utils/helper";
import { formatNumber } from "@/utils/format";
import { HeaderForm } from "@/components/OrderForm/components/HeaderForm";
import { AppDropdown } from "@/components";
import { OPTIONS_ORDER_TYPE } from "@/utils/contants";
import { useMediaQuery } from "react-responsive";
import { AppNumber } from "../../../AppNumber";

export const OrderFormSellMarket = ({
  onShowSettings,
  onShowSelectWallet,
  totalBalanceTokenBase,
  setOrderType,
  orderType,
  pair,
}: {
  onShowSettings: () => void;
  onShowSelectWallet: () => void;
  setOrderType: (value: string) => void;
  totalBalanceTokenBase: string | number;
  orderType: any;
  pair: TPair;
}) => {
  const [sellPercent, setSellPercent] = useState<any>("");

  // estimate
  const tokenAmountEstimateRef = useRef<any>("");
  const [tokenAmountEstimate, setTokenAmountEstimate] = useState<any>("");
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const { useAggregator } = useAggregatorAutoToggle(pair?.createdAt);

  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const { quickSell, estimateQuickSell } = useOrder();
  const targetConvertPercent = useRef("1");
  const orderSettings = Storage.getOrderSettings();
  const isMobile = useMediaQuery({ query: "(max-width: 992px)" });

  useEffect(() => {
    setSellPercent("");
  }, [orderType]);

  const debouncedEstimateQuickSell = useCallback(
    debounce((pair, sellPercent) => {
      estimateQuickSell(pair, +sellPercent).then((totalAmountOut) => {
        if (totalAmountOut) {
          tokenAmountEstimateRef.current = multipliedBN(
            totalAmountOut,
            targetConvertPercent.current
          );
          setTokenAmountEstimate(tokenAmountEstimateRef.current);
        }
      });
    }, 1500),
    [sellPercent, pair?.pairId]
  );

  useEffect(() => {
    tokenAmountEstimateRef.current = "";
    setTokenAmountEstimate(tokenAmountEstimateRef.current);
    if (!accessToken || !sellPercent) return;
    debouncedEstimateQuickSell(pair, sellPercent);

    return () => {
      debouncedEstimateQuickSell.cancel();
    };
  }, [accessToken, sellPercent]);

  const onOrderSuccess = () => {
    setSellPercent("");
    setTokenAmountEstimate("");
    tokenAmountEstimateRef.current = "";
  };

  const createOrderSellMarket = async () => {
    if (isLoading) return;

    setIsLoading(true);
    await quickSell(pair, +sellPercent, useAggregator, () => {
      onOrderSuccess();
      setIsLoading(false);
    });
    setIsLoading(false);
  };

  const createOrder = async () => {
    if (!+sellPercent || new BigNumber(sellPercent).isZero()) {
      toastError("Error", "Please input amount!");
      return;
    }

    if (!+sellPercent) {
      toastError("Error", "Amount too small!");
      return;
    }

    createOrderSellMarket().then();
    return;
  };

  const _renderTextBtn = () => {
    const sellAmount = new BigNumber(dividedBN(sellPercent, 100)).multipliedBy(
      totalBalanceTokenBase
    );
    return (
      <>
        <div className="md:body-md-medium-14 body-sm-medium-12 text-brand-500 flex items-center gap-1">
          Sell{" "}
          {!!+sellAmount && (
            <AppNumber
              value={sellAmount}
              decimals={pair?.tokenBase?.decimals}
            />
          )}
          {pair?.tokenBase?.symbol}
        </div>
        {!!+tokenAmountEstimate && (
          <div className="body-xs-medium-10 text-white-500">
            (≈
            {formatNumber(tokenAmountEstimate, pair?.tokenQuote.decimals)}{" "}
            {pair?.tokenQuote?.symbol})
          </div>
        )}
      </>
    );
  };

  return (
    <div>
      <HeaderForm
        pair={pair}
        orderType={orderType}
        onShowSelectWallet={onShowSelectWallet}
        setOrderType={setOrderType}
        totalBalanceTokenBase={totalBalanceTokenBase}
      />

      {isMobile && (
        <div className="tablet bg-black-900 border-white-50 mt-3 flex h-[34px] items-center rounded-[4px] border">
          <AppDropdown
            options={OPTIONS_ORDER_TYPE}
            onSelect={(value) => {
              setOrderType(value);
            }}
            className="border-white-50 w-[64px] rounded-none border-r border-solid"
            value={orderType}
          />

          <div className="body-sm-regular-12 text-white-300 w-full text-center">
            Market Price
          </div>
        </div>
      )}

      <AmountForm
        value={sellPercent}
        onChange={setSellPercent}
        configs={{
          steps: {
            type: "percent",
            values: orderSettings?.defaultSellPercent,
          },
        }}
      />

      <ButtonOrderSubmit
        isLoading={isLoading}
        onShowSettings={onShowSettings}
        pair={pair}
        sellPercent={sellPercent}
        sellType={orderType}
        createOrder={createOrder}
        text={_renderTextBtn()}
      />
    </div>
  );
};
