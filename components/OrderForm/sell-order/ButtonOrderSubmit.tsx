import { ButtonSignIn } from "@/components/OrderForm/ButtonSignin";
import { ButtonAddWallet } from "@/components/OrderForm/ButtonAddWallet";
import { ButtonAddFund } from "@/components/OrderForm/ButtonAddFund";
import { getDexToWhenAfterGraduated, isDexHasBondingCurve } from "@/utils/dex";
import { AppButton } from "@/components";
import * as React from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { useRaidenxWallet } from "@/hooks";
import { TPair } from "@/types";
import { CoinTip, GasIcon, SettingsIcon, SlippageIcon } from "@/assets/icons";
import { OrderFormType } from "@/enums";
import { TRADE_TYPE } from "@/enums/trade.enum";
import { ClipLoader } from "react-spinners";

export const ButtonOrderSubmit = ({
  sellPercent,
  sellType,
  createOrder,
  onShowSettings,
  text,
  pair,
  isLoading,
}: {
  sellType: string;
  isLoading?: boolean;
  sellPercent: any;
  text: any;
  createOrder: () => void;
  onShowSettings: () => void;
  pair: TPair;
}) => {
  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const wallets = useSelector((state: RootState) => state.user.wallets);
  const { activeTotalSuiBalance } = useRaidenxWallet();

  const settingsQuickOrder = useSelector(
    (state: RootState) => state.user.settingsQuickOrder
  );

  const settingsLimitOrder = useSelector(
    (state: RootState) => state.user.settingsLimitOrder
  );

  const _renderButton = () => {
    if (!accessToken) {
      return (
        <ButtonSignIn type={TRADE_TYPE.SELL} className="tablet:mt-2 !mt-0" />
      );
    }

    if (!wallets.length) {
      return (
        <ButtonAddWallet type={TRADE_TYPE.SELL} className="tablet:mt-2 !mt-0" />
      );
    }

    if (!+activeTotalSuiBalance) {
      return (
        <ButtonAddFund text={"Add Gas Fee"} className="tablet:mt-2 !mt-0" />
      );
    }

    if (+sellPercent > 100) {
      return <ButtonAddFund className="tablet:mt-2 !mt-0" />;
    }
    if (
      isDexHasBondingCurve(pair?.dex?.dex) &&
      Number(pair?.bondingCurve || 0) >= 1
    ) {
      return (
        <AppButton size="large" disabled variant="buy">
          {pair?.graduatedSlug
            ? `Migrated to ${getDexToWhenAfterGraduated(pair?.dex?.dex)}`
            : `Migrating to ${getDexToWhenAfterGraduated(pair?.dex?.dex)}`}
        </AppButton>
      );
    }

    return (
      <div
        className={`tablet:mt-2 action-sm-medium-14 border-white-150 bg-brand-800 mt-0 flex h-[40px] flex-col items-center justify-center rounded-[6px] border px-2 ${
          isLoading ? "cursor-not-allowed opacity-50" : "cursor-pointer"
        }`}
        onClick={createOrder}
      >
        {isLoading ? <ClipLoader color="#8d93b7" size={15} /> : text}
      </div>
    );
  };

  const getTipAmount = () => {
    if (sellType === OrderFormType.MARKET) {
      return settingsQuickOrder.tipAmount || "--";
    }

    return settingsLimitOrder.tipAmount || "--";
  };

  const getSlippage = () => {
    if (sellType === OrderFormType.MARKET) {
      return settingsQuickOrder.slippage || "--";
    }

    return settingsLimitOrder.slippage || "--";
  };

  const getGasPrice = () => {
    if (sellType === OrderFormType.MARKET) {
      return settingsQuickOrder.gasPrice || "--";
    }

    return settingsLimitOrder.gasPrice || "--";
  };

  return (
    <>
      <div className="tablet:mb-3 mt-9 flex items-center justify-between gap-2">
        <div className="flex gap-2">
          <div className="text-neutral-alpha-500 body-sm-medium-12 flex w-max items-center gap-[4px]">
            <GasIcon />
            <div>{getGasPrice()}</div>
          </div>
          <div className="text-neutral-alpha-500 body-sm-medium-12 border-white-50 flex w-max items-center gap-[4px] border-l pl-2">
            <CoinTip />
            <div>{getTipAmount()}</div>
          </div>

          <div className="text-neutral-alpha-500 body-sm-medium-12 flex gap-1">
            <SlippageIcon />
            {getSlippage()}%
          </div>
        </div>
        <div className="tablet:hidden w-full">{_renderButton()}</div>

        <div
          onClick={onShowSettings}
          className="border-neutral-alpha-500 tablet:p-0 tablet:border-none text-neutral-alpha-500 body-sm-medium-12 hover:text-neutral-alpha-1000 flex cursor-pointer gap-1 rounded-md border border-solid p-2"
        >
          <SettingsIcon className="h-5 w-5" />
          <span className="tablet:inline hidden">Settings</span>
        </div>
      </div>

      <div className="tablet:block hidden">{_renderButton()}</div>
    </>
  );
};
