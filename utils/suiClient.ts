import {
  CoinBalance,
  CoinMetadata,
  CoinStruct,
  getFullnodeUrl,
  GetObjectParams,
  SuiClient,
} from "@mysten/sui/client";
import BigNumber from "bignumber.js";
import {
  SUI_DECIMALS,
  SUI_TOKEN_ADDRESS_FULL,
  SUI_TOKEN_ADDRESS_SHORT,
  USDC_ADDRESS,
  USDC_DECIMALS,
} from "./contants";
import { sleep } from "./helper";
import retry from "async-retry";

export const suiClient = new SuiClient({ url: getFullnodeUrl("mainnet") });
export const SUAI_DECIMALS = 6;
export const SUAI_ADDRESS =
  "0xbc732bc5f1e9a9f4bdf4c0672ee538dbf56c161afe04ff1de2176efabdf41f92::suai::SUAI";

export const RETRY_MAX_ATTEMPT = 2;
export const RETRY_MIN_TIMEOUT = 1000;
export const RETRY_MAX_TIMEOUT = 2000;

export const getSuiClient = (attempt: number = 0) => {
  let rpcUrl = "";
  switch (attempt) {
    case 0:
      rpcUrl = getFullnodeUrl("mainnet");
      break;
    case 1:
      rpcUrl = "https://sui-mainnet.public.blastapi.io";
      break;
    case 2:
      rpcUrl = "https://sui-mainnet.nodeinfra.com";
      break;
    default:
      rpcUrl =
        "https://sui-mainnet.blockvision.org/v1/2osaezLZ3iUhDNTboJxvrWcDauY";
      break;
  }
  // console.log(`getSuiClient ${attempt}`, rpcUrl);
  return new SuiClient({ url: rpcUrl });
};

export const getCoinBalanceOnchain = async (
  walletAddress: string,
  tokenAddress: string
): Promise<string> => {
  let client = suiClient;
  return await retry(
    async () => {
      const balance = await client.getBalance({
        owner: walletAddress,
        coinType: tokenAddress,
      });

      return balance.totalBalance;
    },
    {
      retries: RETRY_MAX_ATTEMPT,
      minTimeout: RETRY_MIN_TIMEOUT,
      maxTimeout: RETRY_MAX_TIMEOUT,
      onRetry: (e, attempt) => {
        console.log(`getCoinBalanceOnchain retry ${attempt}`, e);
        client = getSuiClient(attempt);
      },
    }
  );
};

export const getCoinMetadataOnchain = async (
  tokenAddress: string
): Promise<CoinMetadata | null> => {
  let client = suiClient;
  return await retry(
    async () => {
      const metadata = await client.getCoinMetadata({
        coinType: tokenAddress,
      });

      return metadata;
    },
    {
      retries: RETRY_MAX_ATTEMPT,
      minTimeout: RETRY_MIN_TIMEOUT,
      maxTimeout: RETRY_MAX_TIMEOUT,
      onRetry: (e, attempt) => {
        console.log(`getCoinMetadataOnchain retry ${attempt}`, e);
        client = getSuiClient(attempt);
      },
    }
  );
};

export const getSuiBalanceOnchain = async (
  walletAddress: string
): Promise<string> => {
  return getCoinBalanceOnchain(walletAddress, SUI_TOKEN_ADDRESS_SHORT);
};

export const isSuiBalanceEnough = async (
  walletAddress: string,
  amount: BigNumber | string | number
) => {
  const balance = await getSuiBalanceOnchain(walletAddress);
  return new BigNumber(balance || 0).gte(amount || 0);
};

export async function getOwnerCoinOnchain(
  walletAddress: string,
  tokenAddress: string
): Promise<[(CoinStruct & { owner: string })[], BigNumber]> {
  let client = suiClient;
  return await retry(
    async () => {
      const coins = await client.getCoins({
        owner: walletAddress,
        coinType: tokenAddress,
      });
      return [
        coins.data.map((coin) => ({ ...coin, owner: walletAddress })),
        coins.data.reduce(
          (prev, coinStruct) => BigNumber(coinStruct.balance).plus(prev),
          BigNumber(0)
        ),
      ];
    },
    {
      retries: RETRY_MAX_ATTEMPT,
      minTimeout: RETRY_MIN_TIMEOUT,
      maxTimeout: RETRY_MAX_TIMEOUT,
      onRetry: (e, attempt) => {
        console.log(`getOwnerCoinOnchain retry ${attempt}`, e);
        client = getSuiClient(attempt);
      },
    }
  );
}

export async function getOwnerCoinsOnchain(
  walletAddress: string
): Promise<(CoinStruct & { owner: string })[]> {
  let client = suiClient;
  return await retry(
    async () => {
      const coins = await client.getAllCoins({
        owner: walletAddress,
      });
      let userCoins = coins.data.map((coin) => ({
        ...coin,
        owner: walletAddress,
      }));
      let hasNextPage = coins.hasNextPage;
      let nextCursor = coins.nextCursor;
      while (hasNextPage) {
        const nextCoins = await client.getAllCoins({
          owner: walletAddress,
          cursor: nextCursor,
        });
        userCoins = userCoins.concat(
          nextCoins.data.map((coin) => ({ ...coin, owner: walletAddress }))
        );
        hasNextPage = nextCoins.hasNextPage;
        nextCursor = nextCoins.nextCursor;
        await sleep(1000);
      }
      return userCoins;
    },
    {
      retries: RETRY_MAX_ATTEMPT,
      minTimeout: RETRY_MIN_TIMEOUT,
      maxTimeout: RETRY_MAX_TIMEOUT,
      onRetry: (e, attempt) => {
        console.log(`getOwnerCoinsOnchain retry ${attempt}`, e);
        client = getSuiClient(attempt);
      },
    }
  );
}

export async function getReferenceGasPrice() {
  let client = suiClient;
  return await retry(
    async () => {
      return client.getReferenceGasPrice();
    },
    {
      retries: RETRY_MAX_ATTEMPT,
      minTimeout: RETRY_MIN_TIMEOUT,
      maxTimeout: RETRY_MAX_TIMEOUT,
      onRetry: (e, attempt) => {
        client = getSuiClient(attempt);
        console.log(`getReferenceGasPrice retry ${attempt}`, e);
      },
    }
  );
}

export async function getSuiObject(input: GetObjectParams) {
  let client = suiClient;
  return await retry(
    async () => {
      return await client.getObject(input);
    },
    {
      retries: RETRY_MAX_ATTEMPT,
      minTimeout: RETRY_MIN_TIMEOUT,
      maxTimeout: RETRY_MAX_TIMEOUT,
      onRetry: (e, attempt) => {
        client = getSuiClient(attempt);
        console.log(`getSuiObject retry ${attempt}`, e);
      },
    }
  );
}

export async function getAllBalanceOnchain(
  walletAddress: string
): Promise<(CoinBalance & { owner: string })[]> {
  let client = suiClient;
  return await retry(
    async () => {
      const balances = await client.getAllBalances({
        owner: walletAddress,
      });
      const userBalances = balances.map((balance) => ({
        ...balance,
        owner: walletAddress,
      }));
      return userBalances;
    },
    {
      retries: RETRY_MAX_ATTEMPT,
      minTimeout: RETRY_MIN_TIMEOUT,
      maxTimeout: RETRY_MAX_TIMEOUT,
      onRetry: (e, attempt) => {
        console.log(`getOwnerCoinsOnchain retry ${attempt}`, e);
        client = getSuiClient(attempt);
      },
    }
  );
}

export async function getCoinDecimalsOnchain(
  tokenAddress: string
): Promise<number> {
  if (
    tokenAddress === SUI_TOKEN_ADDRESS_SHORT ||
    tokenAddress === SUI_TOKEN_ADDRESS_FULL
  ) {
    return SUI_DECIMALS;
  }
  if (tokenAddress === USDC_ADDRESS) {
    return USDC_DECIMALS;
  }
  if (tokenAddress === SUAI_ADDRESS) {
    return SUAI_DECIMALS;
  }
  const metadata = await getCoinMetadataOnchain(tokenAddress);
  return metadata?.decimals || SUI_DECIMALS;
}
