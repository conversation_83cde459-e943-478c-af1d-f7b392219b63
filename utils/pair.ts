import { normalizeStructTag } from "@mysten/sui/utils";
import BigNumber from "bignumber.js";
import { EDex, isMemeDexForNetwork } from "@/enums/dex.enum";
import { BROADCAST_EVENTS } from "@/libs/broadcast";
import { TPair, TPairStats, TPosition, TSocial, TWebsite } from "@/types";
import {
  LOW_LIQUIDITY,
  LOW_LIQUIDITY_FUNZONE,
  NETWORKS,
  SUI_DECIMALS,
  SUI_TOKEN_ADDRESS_FULL,
  SUI_TOKEN_ADDRESS_SHORT,
  USDC_ADDRESS,
} from "./contants";
import { MOVE_PUMP_INITIAL_RESERVE, TURBOS_FUN_INITIAL_RESERVE } from "./dex";
import { dividedBN, isZero, minusBN, multipliedBN } from "./helper";
import { TOrder } from "@/types/order.type";
import { isEmpty } from "lodash";

const getHttpsUrl = (url: string) => {
  if (!url) return "";
  if (url.startsWith("http") || url.startsWith("https")) {
    return url;
  }
  return `https://${url}`;
};

export const getLinkWebsitePair = (data: TWebsite[]) => {
  if (!data || !data.length) return "";

  const websiteUrl =
    data.find((item) => item?.label?.toLowerCase() === "website")?.url || "";

  return getHttpsUrl(websiteUrl);
};

export const getLinkSocialPair = (data: TSocial[], key: string) => {
  if (!data || !data.length) return "";

  const socialUrl =
    data.find((item) => item?.type?.toLowerCase() === key)?.url || "";

  return getHttpsUrl(socialUrl);
};

export const convertCreatedAtPair2Seconds = (pair: TPair) => {
  if (!pair.createdAt) {
    return 0;
  }
  const createdAt = pair.createdAt.replace("+00:00:00", "");
  const date = new Date(createdAt);
  return Math.floor(date.getTime() / 1000);
};

export const overridePairStatIfNeed = (pairStatsRes: TPairStats) => {
  const resolutions = ["5m", "1h", "6h", "24h"];

  for (let i = 0; i < resolutions.length; i++) {
    const resolution = resolutions[i];
    if (new BigNumber(pairStatsRes.percent[resolution]).isZero() && i > 0) {
      pairStatsRes.percent[resolution] =
        pairStatsRes.percent[resolutions[i - 1]];
    }
  }
  return pairStatsRes;
};

export const getCirculatingSupply = (pair: TPair) => {
  if (
    pair?.tokenBase?.circulatingSupply &&
    !isZero(pair?.tokenBase?.circulatingSupply)
  ) {
    return pair?.tokenBase?.circulatingSupply;
  }
  if (isZero(pair?.tokenBase?.totalSupply)) {
    return "0";
  }

  const tokenBurned = pair?.tokenBase?.amountBurned || 0;

  if (!isZero(tokenBurned)) {
    return minusBN(
      pair?.tokenBase?.totalSupply,
      tokenBurned
      // dividedBN(tokenBurned, 10 ** pair?.tokenBase.decimals),
    );
  }
  return pair?.tokenBase?.totalSupply;
};

export const getFormatterForSocialsCollectedEvent = (data: any) => {
  return {
    tokenBase: {
      address: data.tokenAddress,
    },
    logoImageUrl: data.logoImageUrl,
    bannerImageUrl: data.bannerImageUrl,
    websites: data.websites,
    socials: data.socials,
  };
};

export const getFormatterForAuditCheckedEvent = (data: any) => {
  return {
    pairId: data.pairId,
    audit: {
      mintAuthority: data?.mintAuthority,
      freezeAuthority: data?.freezeAuthority,
      deployer: data?.coinDev,
      amountBurned: data?.amountBurned,
    },
  };
};

export const getFormatterForBondingCurveEvent = (data: any) => {
  return {
    pairId: data.pairId,
    bondingCurve: data.bondingCurve,
    poolId: data.poolId,
    tokenAddress: data.tokenAddress,
  };
};

export const getFormatterForPairStatsChangedEvent = (data: any) => {
  return {
    pairId: data.pairId,
    liquidity: data.liquidity,
    liquidityUsd: data.liquidityUsd,
    totalTxns: data.totalTxns,
    buyTxns: data.buyTxns,
    sellTxns: data.sellTxns,
    volumeUsd: data.volumeUsd,
    marketCapUsd: data.marketCapUsd,
    priceUsd: data.priceUsd,
    priceSui: data.priceSui,
    reserveQuote: data.reserveQuote,
    bondingCurve: data.bondingCurve,
    stats: {
      percent: data.percent,
      buyTxn: data.buyTxn,
      sellTxn: data.sellTxn,
      totalNumTxn: data.totalNumTxn,
      volume: data.volume,
    },
  };
};

export const getRealReserveQuote = (
  dexKey: string,
  reserveQuote: string | number
) => {
  if (dexKey === EDex.MOVEPUMP) {
    return new BigNumber(reserveQuote).minus(MOVE_PUMP_INITIAL_RESERVE);
  }

  if (dexKey === EDex.TURBOSFUN) {
    return new BigNumber(reserveQuote).minus(TURBOS_FUN_INITIAL_RESERVE);
  }

  return reserveQuote;
};

export const getClassColor = (value: string | number | BigNumber) => {
  if (new BigNumber(value).comparedTo(5000) < 0) {
    return "text-white-600";
  }

  if (new BigNumber(value).comparedTo(50000) < 0) {
    return "text-white-1000";
  }

  if (new BigNumber(value).comparedTo(500000) < 0) {
    return "text-blue-500";
  }
  if (new BigNumber(value).comparedTo(500000) > 0) {
    return "text-[#FCD34B]";
  }

  return "text-white-600";
};

export const filterAuditChecked = (pairs: TPair[], filter: any) => {
  let filterPairs = pairs;

  if (filter && filter.mintAuthDisabled) {
    filterPairs = filterPairs.filter((pair) => {
      return !pair?.tokenBase?.mintAuthority;
    });
  }

  if (filter && filter.freezeAuthDisabled) {
    filterPairs = filterPairs.filter((pair) => {
      return !pair?.tokenBase?.freezeAuthority;
    });
  }

  if (filter && filter.lpBurned) {
    filterPairs = filterPairs.filter((pair) => {
      return (
        new BigNumber(
          multipliedBN(dividedBN(pair.lpBurned || 0, pair.lpSupply || 0), 100)
        ).comparedTo(65) >= 0
      );
    });
  }

  if (filter && filter.top10Holders) {
    filterPairs = filterPairs.filter((pair) => {
      return (
        new BigNumber(pair?.tokenBase?.top10HolderPercent).comparedTo(15) <= 0
      );
    });
  }

  if (filter && filter.devBalance) {
    filterPairs = filterPairs.filter((pair) => {
      return (
        new BigNumber(pair?.tokenBase?.deployerBalancePercent).comparedTo(10) <=
        0
      );
    });
  }

  return filterPairs;
};

export const isFilterValidPair = (pair: TPair, filter: any) => {
  if (filter?.mintAuthDisabled && pair?.tokenBase?.mintAuthority) {
    return false;
  }

  if (filter?.freezeAuthDisabled && pair?.tokenBase?.freezeAuthority) {
    return false;
  }

  if (
    filter?.lpBurned &&
    new BigNumber(
      multipliedBN(dividedBN(pair?.lpBurned || 0, pair?.lpSupply || 0), 100)
    ).comparedTo(65) < 0
  ) {
    return false;
  }

  if (
    filter?.top10Holders &&
    new BigNumber(pair?.tokenBase?.top10HolderPercent || 0).comparedTo(15) > 0
  ) {
    return false;
  }

  if (
    filter?.devBalance &&
    new BigNumber(pair?.tokenBase?.deployerBalancePercent || 0).comparedTo(10) >
      0
  ) {
    return false;
  }
  if (filter?.liquidity) {
    const [min, max] = filter.liquidity.split("-").map(Number);
    if (
      new BigNumber(pair?.liquidity || 0).comparedTo(min) < 0 ||
      new BigNumber(pair?.liquidity || 0).comparedTo(max) > 0
    ) {
      return false;
    }
  }

  if (filter?.volume) {
    const [min, max] = filter.volume.split("-").map(Number);
    if (
      new BigNumber(pair?.volumeUsd || 0).comparedTo(min) < 0 ||
      new BigNumber(pair?.volumeUsd || 0).comparedTo(max) > 0
    ) {
      return false;
    }
  }

  if (filter?.marketCap) {
    const [min, max] = filter.marketCap.split("-").map(Number);
    if (
      new BigNumber(pair.marketCapUsd || 0).comparedTo(min) < 0 ||
      new BigNumber(pair.marketCapUsd || 0).comparedTo(max) > 0
    ) {
      return false;
    }
  }

  if (filter?.txns) {
    const [min, max] = filter.txns.split("-").map(Number);
    if (
      new BigNumber(pair.totalTxns || 0).comparedTo(min) < 0 ||
      new BigNumber(pair.totalTxns || 0).comparedTo(max) > 0
    ) {
      return false;
    }
  }

  return true;
};

export const buildTemplateUpdatePairStatsToPairTable = () => {
  return {
    broadcastName: BROADCAST_EVENTS.PAIR_STATS_UPDATED,
    fieldKey: "pairId",
    formatter: getFormatterForPairStatsChangedEvent,
    update: (oldData: any, newData: any) => {
      // console.log('UpdatePairStatsToPairTable', oldData, 'newData', newData);
      return {
        ...oldData,
        bondingCurve: newData.bondingCurve,
        buyTxns: newData.buyTxns,
        sellTxns: newData.sellTxns,
        totalTxns: newData.totalTxns,
        volumeUsd: newData.volumeUsd,
        liquidity: newData.liquidity,
        liquidityUsd: newData.liquidityUsd,
        marketCapUsd: newData.marketCapUsd,
        priceSui: newData.priceSui,
        priceUsd: newData.priceUsd,
        reserveBase: newData.reserveBase,
        reserveQuote: newData.reserveQuote,
        stats: {
          ...oldData.stats,
          ...newData,
        },
      };
    },
  };
};

export const buildTemplateUpdateTokenAuditToPairTable = () => {
  return {
    broadcastName: BROADCAST_EVENTS.TOKEN_INFO_AUDIT_UPDATED,
    fieldKey: "pairId",
    formatter: getFormatterForAuditCheckedEvent,
    update: (oldData: any, newData: any) => {
      // console.log('UpdateTokenAuditToPairTable', oldData, 'newData', newData);
      return {
        ...oldData,
        tokenBase: {
          ...oldData.tokenBase,
          ...newData.audit,
        },
      };
    },
  };
};

export const buildTemplateUpdateTokenSocialToPairTable = () => {
  return {
    broadcastName: BROADCAST_EVENTS.TOKEN_INFO_SOCIAL_UPDATED,
    fieldKey: "tokenBase.address",
    formatter: getFormatterForSocialsCollectedEvent,
    update: (oldData: any, newData: any) => {
      // console.log('UpdateTokenSocialToPairTable', oldData, 'newData', newData);
      return {
        ...oldData,
        tokenBase: {
          ...oldData.tokenBase,
          socials: {
            ...oldData.tokenBase.socials,
            websites: newData?.websites,
            socials: newData?.socials,
          },
          logoImageUrl: newData?.logoImageUrl,
          bannerImageUrl: newData?.bannerImageUrl,
        },
      };
    },
  };
};

export const buildTemplateUpdateBondingCurveToPairTable = () => {
  return {
    broadcastName: BROADCAST_EVENTS.BONDING_CURVE_UPDATED,
    fieldKey: "pairId",
    formatter: getFormatterForBondingCurveEvent,
    update: (oldData: any, newData: any) => {
      // console.log('UpdateTokenSocialToPairTable', oldData, 'newData', newData);
      return {
        ...oldData,
        bondingCurve: newData?.bondingCurve,
      };
    },
  };
};

export const isPairWithNativeToken = (
  pair?: TPair | TPosition | TOrder,
  network: NETWORKS = NETWORKS.SUI
) => {
  if (isEmpty(pair)) return false;

  switch (network) {
    case NETWORKS.SUI:
      return (
        pair?.tokenQuote?.address &&
        normalizeStructTag(pair?.tokenQuote?.address) ===
          normalizeStructTag(SUI_TOKEN_ADDRESS_SHORT)
      );
    case NETWORKS.HYPEREVM:
      return (
        pair?.tokenQuote?.address &&
        pair?.tokenQuote?.address === "HYPE_ADDRESS"
      );
    case NETWORKS.SOMNIA:
      return (
        pair?.tokenQuote?.address &&
        pair?.tokenQuote?.address === "SOMNIA_ADDRESS"
      );
  }
};

export const isPairWithUSDC = (pair?: TPair | TPosition) => {
  try {
    return (
      pair?.tokenQuote?.address &&
      normalizeStructTag(pair?.tokenQuote?.address) ===
        normalizeStructTag(USDC_ADDRESS)
    );
  } catch (error) {
    return false;
  }
};

export const isBuyBySuiToken = (addressTokenQuoteSelected: string) => {
  try {
    return (
      normalizeStructTag(addressTokenQuoteSelected) ===
      normalizeStructTag(SUI_TOKEN_ADDRESS_SHORT)
    );
  } catch (error) {
    return false;
  }
};

export const getTokenQuoteDecimals = (quoteAddress: string) => {
  const SUAI_DECIMALS = 6;
  return quoteAddress === SUI_TOKEN_ADDRESS_FULL ? SUI_DECIMALS : SUAI_DECIMALS;
};

export const overrideDataTableFilter = (dataTableFilter: any) => {
  const MAX_VALUE = "100000000000000";
  const fields = ["liquidity", "marketCap", "txns", "volume"];

  fields.forEach((field) => {
    if (dataTableFilter[field]) {
      let [min, max] = dataTableFilter[field].split("-").map(Number);

      if (!min) min = "0";
      if (!max) max = MAX_VALUE;

      dataTableFilter[field] = `${min}-${max}`;
    }
  });

  return dataTableFilter;
};

export const isLowLiquidityPair = (
  liquidityUsd: string,
  dex: EDex,
  network: string = "sui"
) => {
  if (isMemeDexForNetwork(dex, network)) {
    return new BigNumber(liquidityUsd).comparedTo(LOW_LIQUIDITY_FUNZONE) < 0;
  }
  return new BigNumber(liquidityUsd).comparedTo(LOW_LIQUIDITY) < 0;
};

export const isTrendingPair = (pair: TPair) => {
  return (
    pair?.trendingRank &&
    Number(pair?.trendingRank) > 0 &&
    Number(pair?.trendingRank) <= 30
  );
};
