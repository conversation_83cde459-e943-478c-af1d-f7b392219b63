import { CoinStruct } from "@mysten/sui/client";
import { Transaction } from "@mysten/sui/transactions";
import { normalizeStructTag, normalizeSuiAddress } from "@mysten/sui/utils";
import BigNumber from "bignumber.js";
import { TCoinMetadata, TPosition } from "@/types";
import {
  SUI_TOKEN_ADDRESS_FULL,
  SUI_TOKEN_ADDRESS_SHORT,
  SUI_TOKEN_METADATA,
} from "@/utils/contants";
import { isZero } from "@/utils/helper";
import {
  getOwnerCoinOnchain,
  getReferenceGasPrice,
  getSuiObject,
} from "@/utils/suiClient";
import BaseSimulate from "../BaseSimulate";

export const CETUS_PACKAGE =
  "0x91d556df93114e75aaeeb8b3a1b1682cee265c00cd154736a97e8b2079786f47";
export const CETUS_MODULE = "cetus_router";
export const CETUS_CONFIG_OBJECT_ID =
  "0xdaa46292632c3c4d8f31f23ea0f9b36a28ff3677e9684980e4438403a67a3d8f";

export default class CetusSimulate extends BaseSimulate {
  public name = "CetusSimulate";
  private buildBuyTransaction = async (
    walletAddress: string,
    exactAmountIn: BigNumber | string | number,
    tokenOut: TCoinMetadata,
    gasBasePrice: bigint,
    poolObjectId: string,
    tokenIn: TCoinMetadata = SUI_TOKEN_METADATA
  ) => {
    const tx = new Transaction();
    tx.setGasBudget(10000000);
    tx.setSender(walletAddress);
    tx.setGasPrice(gasBasePrice);

    let coinTokenIn = null;

    if (
      tokenIn.address === SUI_TOKEN_ADDRESS_SHORT ||
      tokenIn.address === SUI_TOKEN_ADDRESS_FULL
    ) {
      const [coin] = tx.splitCoins(tx.gas, [
        tx.pure.u64(exactAmountIn.toString()),
      ]);
      coinTokenIn = coin;
    } else {
      const [tokenInObjects] = await getOwnerCoinOnchain(
        walletAddress,
        tokenIn.address
      );
      if (tokenInObjects.length > 1) {
        tx.mergeCoins(
          tokenInObjects[0].coinObjectId,
          tokenInObjects.slice(1).map((coin) => coin.coinObjectId)
        );
      }
      coinTokenIn = tx.object(tokenInObjects[0].coinObjectId);
    }

    const { data: poolObject } = await getSuiObject({
      id: poolObjectId,
      options: {
        showContent: true,
      },
    });
    const poolType = (poolObject?.content as any)?.type;
    const { tokenXAddress, tokenYAddress } =
      this.extractTokenX2YFromPoolType(poolType);
    if (!tokenXAddress || !tokenYAddress) {
      throw new Error("Invalid pool type");
    }
    const xToY =
      tokenXAddress &&
      normalizeStructTag(tokenXAddress) === normalizeStructTag(tokenIn.address);

    let tokenXObject = null;
    let tokenYObject = null;

    if (
      normalizeStructTag(tokenXAddress) === normalizeStructTag(tokenIn.address)
    ) {
      tokenXObject = coinTokenIn;
      tokenYObject = tx.moveCall({
        target: "0x2::coin::zero",
        typeArguments: [tokenYAddress],
      });
    } else {
      tokenXObject = tx.moveCall({
        target: "0x2::coin::zero",
        typeArguments: [tokenXAddress],
      });
      tokenYObject = coinTokenIn;
    }
    tx.moveCall({
      target: `${CETUS_PACKAGE}::${CETUS_MODULE}::buy_exact_in`,
      typeArguments: [tokenXAddress, tokenYAddress],
      arguments: [
        tx.object(CETUS_CONFIG_OBJECT_ID),
        tx.object(poolObjectId),
        tokenXObject,
        tokenYObject,
        tx.pure.u64(0),
        tx.pure.u128(
          xToY ? BigInt("4295048016") : BigInt("79226673515401279992447579055")
        ),
        tx.object("0x6"),
        tx.pure.bool(xToY ? true : false), // buy = false, sell = true
        tx.pure.string("abc"),
      ],
    });

    return { tx, amountOut: NaN };
  };

  private buildSellTransaction = async (
    walletAddress: string,
    exactAmountIn: BigNumber,
    tokenIn: TCoinMetadata,
    gasBasePrice: bigint,
    coinObjs: (CoinStruct & { owner: string })[],
    poolObjectId: string
  ) => {
    const tx = new Transaction();
    tx.setGasBudget(10000000);
    tx.setSender(walletAddress);
    tx.setGasPrice(gasBasePrice);

    if (coinObjs.length > 1) {
      tx.mergeCoins(
        coinObjs[0].coinObjectId,
        coinObjs.slice(1).map((coin) => coin.coinObjectId)
      );
    }

    const coinExactAmountInObj = tx.splitCoins(coinObjs[0].coinObjectId, [
      tx.pure.u64(exactAmountIn.toString()),
    ]);

    const { data: poolObject } = await getSuiObject({
      id: poolObjectId,
      options: {
        showContent: true,
      },
    });
    const poolType = (poolObject?.content as any)?.type;
    const { tokenXAddress, tokenYAddress } =
      this.extractTokenX2YFromPoolType(poolType);
    if (!tokenXAddress || !tokenYAddress) {
      throw new Error("Invalid pool type");
    }
    const xToY =
      tokenXAddress &&
      normalizeStructTag(tokenXAddress) === normalizeStructTag(tokenIn.address);

    let tokenXObject = null;
    let tokenYObject = null;

    if (
      normalizeStructTag(tokenXAddress) === normalizeStructTag(tokenIn.address)
    ) {
      tokenXObject = coinExactAmountInObj;
      tokenYObject = tx.moveCall({
        target: "0x2::coin::zero",
        typeArguments: [tokenYAddress],
      });
    } else {
      tokenXObject = tx.moveCall({
        target: "0x2::coin::zero",
        typeArguments: [tokenXAddress],
      });
      tokenYObject = coinExactAmountInObj;
    }

    tx.moveCall({
      target: `${CETUS_PACKAGE}::${CETUS_MODULE}::sell_exact_in`, // package
      typeArguments: [tokenXAddress, tokenYAddress],
      arguments: [
        tx.object(
          CETUS_CONFIG_OBJECT_ID // dex config cetus
        ),
        tx.object(
          poolObjectId // pool address
        ),
        tokenXObject,
        tokenYObject,
        tx.pure.u64(0), // amountOutMin
        tx.pure.u128(
          xToY ? BigInt("4295048016") : BigInt("79226673515401279992447579055")
        ), // hardcode
        tx.object("0x6"), // clock
        tx.pure.bool(xToY ? true : false), // buy = false, sell = true
        tx.pure.string("abc"), // orderId
      ],
    });
    return { tx, amountOut: NaN };
  };

  public extractBaseTokenOut = async (
    walletAddress: string,
    amountIn: string,
    tokenIn: TCoinMetadata = SUI_TOKEN_METADATA,
    tokenOut: TCoinMetadata,
    poolObjectId: string | undefined,
    gasBasePrice: bigint
  ) => {
    if (!poolObjectId) {
      throw new Error("Pool object id not found");
    }

    return this.buildBuyTransaction(
      walletAddress,
      amountIn,
      tokenOut,
      gasBasePrice,
      poolObjectId,
      tokenIn
    );
  };

  public extractQuoteTokenOut = async (
    walletAddress: string,
    tokenIn: TCoinMetadata,
    tokenOut: TCoinMetadata,
    sellPercent: number,
    poolObjectId: string | undefined,
    gasBasePrice: bigint
  ) => {
    if (!poolObjectId) {
      throw new Error("Pool object id not found");
    }

    const [coinObjs, currentAmount] = await getOwnerCoinOnchain(
      walletAddress,
      tokenIn.address
    );

    if (currentAmount.isZero()) {
      throw new Error("Insufficient balance");
    }

    const exactAmountIn = currentAmount
      .multipliedBy(sellPercent)
      .div(100)
      .integerValue(BigNumber.ROUND_FLOOR);

    return this.buildSellTransaction(
      walletAddress,
      exactAmountIn,
      tokenIn,
      gasBasePrice,
      coinObjs,
      poolObjectId
    );
  };

  public extractQuoteTokenOutPositionWithSponsor = async (
    position: TPosition,
    userAllCoins: (CoinStruct & { owner: string })[],
    gasBasePrice?: bigint
  ) => {
    const tokenCoinObjects = userAllCoins.filter(
      (coin) =>
        normalizeStructTag(coin.coinType) ===
          normalizeStructTag(position.token.address) &&
        normalizeSuiAddress(coin.owner) ===
          normalizeSuiAddress(position.walletName)
    );
    if (!tokenCoinObjects) {
      throw new Error("Token not found");
    }
    const tokenBalance = tokenCoinObjects.reduce(
      (prev, coin) => prev.plus(coin.balance),
      new BigNumber(0)
    );

    if (!gasBasePrice) {
      gasBasePrice = await getReferenceGasPrice();
    }

    if (isZero(tokenBalance)) {
      throw new Error("Insufficient balance");
    }

    const exactAmountIn = tokenBalance
      .multipliedBy(100)
      .div(100)
      .integerValue(BigNumber.ROUND_FLOOR);

    const result = await this.buildSellTransaction(
      position.walletName,
      exactAmountIn,
      position.token,
      gasBasePrice,
      tokenCoinObjects,
      position.poolId
    );
    result.tx.setSender(position.walletName);
    return {
      tx: await this.buildSponsoredTransaction(result.tx),
      amountOut: result.amountOut || NaN,
    };
  };
}
