"use client";
import {
  createContext,
  ReactNode,
  useCallback,
  useEffect,
  useState,
} from "react";
import * as React from "react";
import rf from "@/services/RequestFactory";
import { TPair, TPairPrice, TPairTransaction } from "@/types";
import {
  AppBroadcast,
  BROADCAST_EVENTS,
  TBroadcastEvent,
} from "@/libs/broadcast";
import { EDex, TradingType } from "@/enums";
import {
  SOCKETS_ROOMS,
  subscribeSocketRoom,
  unsubscribeSocketRoom,
} from "@/libs/socket";
import { isEmpty } from "lodash";
import { isZero } from "@/utils/helper";
import { setBalance } from "@/store/user.store";
import { useDispatch, useSelector } from "react-redux";
import { RootState, AppDispatch } from "@/store";
import { useNetwork } from "@/context/network";
import { isPairWithNativeToken } from "@/utils/pair";
import { getSuiObject } from "@/utils/suiClient";
import BigNumber from "bignumber.js";
import { isDexHasBondingCurve } from "@/utils/dex";
import { roundToPercent } from "@/utils/format";
import { ModalGraduatedPair } from "@/modals/ModalGraduatedPair";

type RootPairContextType = {
  pair?: TPair;
  pairPrice?: TPairPrice;
  setPair?: (value: TPair) => void;
  walletsActive?: string[];
  setWalletsActive?: (wallets: string[]) => void;
  reserveBase?: string;
  reserveQuote?: string;
  isLoading?: boolean;
  poolObjects?: any;
};

export const RootPairContext = createContext<RootPairContextType>({});

export const RootPairProvider = ({
  children,
  externalPair,
  enableGraduatedPairChecker = true,
}: {
  children: ReactNode;
  externalPair: TPair;
  enableGraduatedPairChecker?: boolean;
}) => {
  console.log("externalPair", externalPair);
  const [pair, setPair] = useState<TPair>(externalPair);
  const [pairPrice, setPairPrice] = useState<TPairPrice>();
  const [isLoading, setIsLoading] = useState(false);
  const [walletsActive, setWalletsActive] = useState<string[]>([]);
  const [reserveBase, setReserveBase] = useState("0");
  const [reserveQuote, setReserveQuote] = useState("0");
  const accessToken = useSelector((state: RootState) => state.user.accessToken);
  const [poolObjects, setPoolObjects] = useState<any>();
  const connectedSocket = useSelector(
    (state: RootState) => state.metadata.connectedSocket
  );
  const { currentNetwork } = useNetwork();

  const dispatch = useDispatch<AppDispatch>();

  const subscribeSockets = useCallback(() => {
    subscribeSocketRoom(
      pair.network,
      SOCKETS_ROOMS.PAIR_DETAIL(pair.network, pair.pairId)
    );
  }, [pair.network, pair.pairId]);

  const unsubscribeSockets = useCallback(() => {
    unsubscribeSocketRoom(
      pair.network,
      SOCKETS_ROOMS.PAIR_DETAIL(pair.network, pair.pairId)
    );
  }, [pair.network, pair.pairId]);

  console.log("pair=====", pair);

  const getPair = useCallback(async () => {
    setIsLoading(true);
    let res: TPair | null = null;
    try {
      res = await rf
        .getRequest("PairRequest")
        .getPair(currentNetwork, pair?.slug);
      if (res) {
        setPair(res);
        setPairPrice({
          price: res.tokenBase.price,
          priceUsd: res.tokenBase.priceUsd,
        });
        setReserveBase(res.reserveBase);
        setReserveQuote(res.reserveQuote);
      }
    } catch (e) {
      console.error(e);
    } finally {
      setIsLoading(false);
    }
  }, [pair?.pairId, pair?.network, pair?.slug]);

  useEffect(() => {
    if (!externalPair) return;
    setPairPrice({
      price: externalPair?.tokenBase?.price,
      priceUsd: externalPair?.tokenBase?.priceUsd,
    });
    setReserveBase(externalPair?.reserveBase);
    setReserveQuote(externalPair?.reserveQuote);
  }, [externalPair?.slug]);

  const handleWhenRefreshData = useCallback(
    (event: TBroadcastEvent) => {
      getPair().then();
    },
    [pair?.pairId, getPair]
  );

  useEffect(() => {
    if (!pair?.pairId || !connectedSocket) return;
    subscribeSockets();

    AppBroadcast.on(BROADCAST_EVENTS.REFRESH_DATA, handleWhenRefreshData);
    return () => {
      unsubscribeSockets();
      AppBroadcast.remove(BROADCAST_EVENTS.REFRESH_DATA, handleWhenRefreshData);
    };
  }, [
    pair?.pairId,
    connectedSocket,
    handleWhenRefreshData,
    subscribeSockets,
    unsubscribeSockets,
  ]);

  const getPoolObject = async () => {
    if (
      pair?.dex?.dex === EDex.MOONBAGS &&
      pair &&
      Number(pair?.bondingCurve) < 1
    ) {
      const { data: poolObject } = (await getSuiObject({
        id: pair?.poolId,
        options: {
          showContent: true,
        },
      })) as any;
      const { content: poolObjectContent } = poolObject;
      const { fields: poolObjectFields } = poolObjectContent;
      setPoolObjects(poolObjectFields);
      return;
    }
    return;
  };

  useEffect(() => {
    if (pair?.poolId && pair?.dex?.dex) {
      getPoolObject().then();
    }
  }, [pair?.poolId, pair?.dex?.dex]);

  const handleWhenPairStatsChange = async (event: TBroadcastEvent) => {
    const data: any = event.detail;
    if (data.pairId !== pair.pairId) {
      return;
    }

    if (data?.reserveQuote) {
      setReserveQuote(data?.reserveQuote);
    }

    if (data?.reserveBase) {
      setReserveBase(data?.reserveBase);
    }

    if (data.graduatedSlug) {
      setPair({
        ...pair,
        graduatedSlug: data.graduatedSlug,
      });
    }
  };

  const handleWhenBondingCurveChange = async (event: TBroadcastEvent) => {
    const data: any = event.detail;
    if (data.pairId !== pair.pairId) {
      return;
    }

    if (data?.bondingCurve) {
      setPair({
        ...pair,
        bondingCurve: data?.bondingCurve,
      });
    }
  };

  const handleWhenNewTransaction = (event: TBroadcastEvent) => {
    const data: TPairTransaction = event.detail;
    if (data.pairId !== pair.pairId) {
      return;
    }

    // fetch pool details
    getPoolObject().then();

    if (
      data?.tradingType !== TradingType.ADD &&
      data?.tradingType !== TradingType.REMOVE
    ) {
      setPairPrice({
        price: data.price,
        priceUsd: data.priceUsd,
      });
    }
  };

  useEffect(() => {
    if (isEmpty(pair)) return;
    AppBroadcast.on(
      BROADCAST_EVENTS.TRANSACTION_CREATED,
      handleWhenNewTransaction
    );
    AppBroadcast.on(
      BROADCAST_EVENTS.PAIR_STATS_UPDATED,
      handleWhenPairStatsChange
    );

    AppBroadcast.on(
      BROADCAST_EVENTS.BONDING_CURVE_UPDATED,
      handleWhenBondingCurveChange
    );

    return () => {
      AppBroadcast.remove(
        BROADCAST_EVENTS.TRANSACTION_CREATED,
        handleWhenNewTransaction
      );
      AppBroadcast.remove(
        BROADCAST_EVENTS.PAIR_STATS_UPDATED,
        handleWhenPairStatsChange
      );
      AppBroadcast.remove(
        BROADCAST_EVENTS.BONDING_CURVE_UPDATED,
        handleWhenBondingCurveChange
      );
    };
  }, [pair?.pairId]);

  const fetchBalances = async (network: string, tokenBase: any) => {
    try {
      const res = await rf
        .getRequest("TokenRequest")
        .getBalanceOf(network, tokenBase?.address);

      if (!res?.length) return;

      res.forEach((item: any) => {
        const balance = item?.balance || 0;
        if (isZero(balance)) return;

        dispatch(
          setBalance({
            network: network,
            token: tokenBase,
            walletAddress: item.walletAddress,
            balance: balance,
            balanceUsd: item.balanceUsd,
          })
        );
      });
    } catch (error) {
      console.error("Error fetching balances:", error);
    }
  };

  useEffect(() => {
    if (accessToken && pair?.pairId) {
      fetchBalances(pair?.network, pair?.tokenBase);
      if (!isPairWithNativeToken(pair, currentNetwork)) {
        fetchBalances(pair?.network, pair?.tokenQuote);
      }
    }
  }, [accessToken, pair?.pairId]);

  const GraduatedPairChecker = () => {
    const [isOpenModalGraduatedPair, setIsOpenModalGraduatedPair] =
      useState<boolean>(false);

    useEffect(() => {
      if (
        !!pair?.graduatedSlug &&
        isDexHasBondingCurve(pair?.dex?.dex) &&
        new BigNumber(roundToPercent(pair?.bondingCurve)).comparedTo(100) >= 0
      ) {
        setIsOpenModalGraduatedPair(true);
      }
    }, [pair]);

    return (
      <>
        {isOpenModalGraduatedPair && (
          <ModalGraduatedPair
            pair={pair}
            isOpen={isOpenModalGraduatedPair}
            onClose={() => setIsOpenModalGraduatedPair(false)}
          />
        )}
      </>
    );
  };

  return (
    <RootPairContext.Provider
      value={{
        pair,
        pairPrice,
        setPair,
        walletsActive,
        setWalletsActive,
        reserveBase,
        reserveQuote,
        isLoading,
        poolObjects,
      }}
    >
      {pair?.pairId ? children : null}
      {enableGraduatedPairChecker && <GraduatedPairChecker />}
    </RootPairContext.Provider>
  );
};
