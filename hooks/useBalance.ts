"use client";

import { TBalance } from "@/types/balance.type";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import { normalizeSuiAddress, normalizeStructTag } from "@mysten/sui/utils";
import { TWallet } from "@/types";
import {
  SUI_TOKEN_ADDRESS_SHORT,
  SUI_TOKEN_ADDRESS_FULL,
} from "@/utils/contants";
import { useCallback, useEffect, useRef } from "react";
import { useNetwork } from "@/context";
import { setBalance } from "@/store/user.store";

export const useBalance = () => {
  const balances = useSelector((state: RootState) => state.user.balances);
  const wallets = useSelector((state: RootState) => state.user.wallets);
  const { currentNetwork } = useNetwork();

  const balancesRef = useRef<TBalance[]>([]);
  const walletsRef = useRef<TWallet[]>([]);

  useEffect(() => {
    balancesRef.current = balances;
  }, [balances]);

  useEffect(() => {
    walletsRef.current = wallets;
  }, [wallets]);

  const getWalletBalance = useCallback(
    (tokenAddress: string, walletAddress: string) => {
      if (!tokenAddress) return "0";

      if (
        tokenAddress === SUI_TOKEN_ADDRESS_FULL ||
        tokenAddress === SUI_TOKEN_ADDRESS_SHORT
      ) {
        return getSuiBalance(walletAddress);
      }
      const tokenBalance =
        balancesRef.current?.find((balance: TBalance) => {
          return (
            balance &&
            balance?.token?.address &&
            balance?.walletAddress &&
            tokenAddress &&
            walletAddress &&
            normalizeStructTag(balance?.token?.address) ===
              normalizeStructTag(tokenAddress) &&
            normalizeSuiAddress(balance?.walletAddress) ===
              normalizeSuiAddress(walletAddress)
          );
        }) || ({} as TBalance);

      return tokenBalance?.balance || "0";
    },
    [balances]
  );

  const getSuiBalance = (walletAddress: string) => {
    const wallet =
      walletsRef.current?.find((wallet: TWallet) => {
        return (
          wallet?.address &&
          walletAddress &&
          normalizeSuiAddress(wallet?.address) ===
            normalizeSuiAddress(walletAddress)
        );
      }) || ({} as TWallet);

    return wallet?.balance || "0";
  };

  return {
    getWalletBalance,
  };
};
